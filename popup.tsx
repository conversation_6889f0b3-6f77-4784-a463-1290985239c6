import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Input, Card, CardBody, Link } from "@heroui/react"
import "./style.css"

function IndexPopup() {
  const [data, setData] = useState("")
  
  return (
    <HeroUIProvider>
      <div className="min-w-[500px] bg-background dark border-transparent bg-[url('https://arc.net/noise-light.png')]">
        <Card className="mb-4">
          <CardBody>
            <h2 className="text-xl font-bold mb-2 text-foreground">
              Welcome to your{" "}
              <Link href="https://www.plasmo.com" isExternal color="primary">
                Plasmo222
              </Link>{" "}
              `Extension!
            </h2>
            <Input
              placeholder="Enter some text..."
              value={data}
              onChange={(e) => setData(e.target.value)}
              className="mb-3"
            />
            <Button
              as={Link}
              href="https://docs.plasmo.com"
              isExternal
              color="primary"
              variant="solid"
            >
              View Docs
            </Button>
          </CardBody>
        </Card>
      </div>
    </HeroUIProvider>
  )
}

export default IndexPopup
