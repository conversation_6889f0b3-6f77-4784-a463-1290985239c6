{"extends": "plasmo/templates/tsconfig.base", "exclude": ["node_modules"], "include": [".plasmo/index.d.ts", "next-env.d.ts", "window.d.ts", "./**/*.ts", "./**/*.tsx"], "compilerOptions": {"paths": {"~*": ["./*"], "@/*": ["./*"]}, "baseUrl": ".", "isolatedModules": true, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "typeRoots": ["./node_modules/@types"], "skipLibCheck": true}}