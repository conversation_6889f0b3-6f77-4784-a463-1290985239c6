import type { PlasmoCSConfig } from "plasmo"
import { useEffect, useState } from "react"
// import { sendToBackground } from "@plasmohq/messaging"
import * as ReactDOM from "react-dom/client"
import { Icon } from "@iconify/react"
import { Button } from "~components/ui/button"
import styled from "@emotion/styled"
import { sendMessageWithRetry, showExtensionError } from "~lib/messaging-utils"

export const config: PlasmoCSConfig = {
  matches: ["https://twitter.com/*", "https://x.com/*"],
  all_frames: false,
  run_at: "document_idle",
}

const StyledButton = styled(Button)`
  position: relative;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 9999px;
  display: flex;
  gap: 6px;
  justify-content: space-between;
  align-items: center;
  font-family: monospace;
  border: 1px solid rgb(83, 100, 113);
  margin-top: 10px;
  cursor: pointer;
  font-size: 14px;
  transition: all .1s ease-in-out;
  outline: none;
  position: relative;
  z-index: 0;
  &:hover {
    background-color: #1d9cf038;
    color: #1d9bf0;
  }
`


// 从用户档案页面提取用户信息的函数
function extractUserInfo() {
  // 尝试从 URL 获取用户名
  const pathname = window.location.pathname;
  const usernameMatch = pathname.match(/^\/([^\/]+)$/);
  const username = usernameMatch ? usernameMatch[1] : null;

  if (!username || username === 'home' || username === 'explore' || username === 'notifications') {
    return null;
  }

  // 尝试获取显示名称和其他信息
  const displayNameElement = document.querySelector('[data-testid="UserName"] span span');
  const displayName = displayNameElement?.textContent || username;

  // 尝试获取用户ID（从页面数据中）
  const scripts = document.querySelectorAll('script');

  let userId = null;

  for (const script of scripts) {
    const content = script.textContent;
    if (content && content.includes('rest_id')) {
      const match = content.match(/"rest_id":"(\d+)"/);
      if (match) {
        userId = match[1];
        break;
      }
    }
  }

  return { username, displayName, userId };
}

function UserExportButton() {
  const [userInfo, setUserInfo] = useState<{ username: string, displayName: string, userId: string | null } | null>(null);

  useEffect(() => {
    const checkUserProfile = () => {
      const info = extractUserInfo();
      if (info) {
        setUserInfo(info);
      }
    };

    // 初始检查
    checkUserProfile();

    // 监听 URL 变化（使用 popstate 事件更高效）
    const handleUrlChange = () => {
      // 延迟检查，等待页面内容更新
      setTimeout(checkUserProfile, 100);
    };

    window.addEventListener('popstate', handleUrlChange);

    // 也监听 pushState 和 replaceState（Twitter SPA 导航）
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function (...args) {
      originalPushState.apply(history, args);
      handleUrlChange();
    };

    history.replaceState = function (...args) {
      originalReplaceState.apply(history, args);
      handleUrlChange();
    };

    return () => {
      window.removeEventListener('popstate', handleUrlChange);
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
    };
  }, []);

  const handleExportClick = async () => {
    if (!userInfo) return;

    try {
      const exportUrl = `tabs/export.html?username=${encodeURIComponent(userInfo.username)}&displayName=${encodeURIComponent(userInfo.displayName)}&userId=${userInfo.userId || ''}`;

      const response = await sendMessageWithRetry({
        name: 'open-tab',
        body: {
          type: 'openTab',
          url: exportUrl
        }
      }, {
        maxRetries: 3,
        onRetry: (attempt) => {
          console.log(`正在重试打开导出页面 (${attempt}/3)...`);
        },
        onContextInvalidated: () => {
          console.warn('扩展上下文失效，使用备用方案');
          // 备用方案：直接打开页面
          const extensionUrl = chrome.runtime.getURL(exportUrl);
          window.open(extensionUrl, '_blank');
        }
      });

      if (!response?.success && response?.error) {
        console.error('打开导出页面失败:', response.error);
        // 备用方案
        const extensionUrl = chrome.runtime.getURL(exportUrl);
        window.open(extensionUrl, '_blank');
      }
    } catch (error: any) {
      showExtensionError(error, '打开导出页面时');

      // 最终备用方案：直接打开页面
      try {
        const exportUrl = `tabs/export.html?username=${encodeURIComponent(userInfo.username)}&displayName=${encodeURIComponent(userInfo.displayName)}&userId=${userInfo.userId || ''}`;
        const extensionUrl = chrome.runtime.getURL(exportUrl);
        window.open(extensionUrl, '_blank');
      } catch (backupError) {
        console.error('备用方案也失败了:', backupError);
        alert('无法打开导出页面，请尝试重新加载页面');
      }
    }
  };

  // 如果不在用户档案页面，不显示按钮
  if (!userInfo) {
    return null;
  }

  return (
    <StyledButton onClick={handleExportClick}>
      <Icon icon="game-icons:shadow-follower" style={{ width: 22, height: 22 }} />
      Export Following / Followers
    </StyledButton>
  );
}

export default function UserProfileExport() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // 等待页面加载完成
    const timer = setTimeout(() => {
      setMounted(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // 添加 path 状态来监听页面变化
  const [currentPath, setCurrentPath] = useState(window.location.pathname);

  // 插入按钮的函数
  const insertExportButton = () => {
    // 检查是否已经插入了按钮，避免重复插入
    if (document.querySelector('.export-button-container')) {
      return;
    }

    // 查找用户档案页面的按钮容器
    const profileTabsContainer = document.querySelector('[data-testid="primaryColumn"] [data-testid="UserName"]')?.parentElement;

    if (!profileTabsContainer) {
      return;
    }

    // 查找 Following 或 Followers 按钮作为参考点
    const followingButton = document.querySelector('a[href*="/following"]');
    const followersButton = document.querySelector('a[href*="/followers"]');

    if (followingButton || followersButton) {
      // 创建按钮容器
      const container = document.createElement('div');
      container.className = 'export-button-container';
      container.style.display = 'inline-flex';
      container.style.alignItems = 'center';

      // 插入到 tabs 容器的末尾
      profileTabsContainer.appendChild(container);

      // 渲染 React 组件到容器中
      const root = ReactDOM.createRoot(container);
      root.render(<UserExportButton />);

    } else {
      console.log('%c [ No following/followers buttons found ]-230', 'font-size:13px; background:red; color:white;');
    }
  };

  // 监听 path 变化的 useEffect
  useEffect(() => {
    if (!mounted) return;

    // 检查是否是用户主页
    const isUserProfilePage = (path: string) => {
      // 匹配格式: /username (但不包括 /username/status/xxx 等子页面)
      return /^\/[^\/]+\/?$/.test(path) &&
        !path.includes('/status/') &&
        !path.includes('/photo/') &&
        !path.includes('/followers') &&
        !path.includes('/following');
    };


    // 清理之前的按钮
    const existingContainers = document.querySelectorAll('.export-button-container');
    if (existingContainers.length > 0) {
      existingContainers.forEach(container => container.remove());
    }

    // 如果是用户主页，延迟插入按钮
    if (isUserProfilePage(currentPath)) {
      const timer = setTimeout(() => {
        insertExportButton();
      }, 300); // 给页面一些时间加载内容

      return () => clearTimeout(timer);
    }
  }, [mounted, currentPath]);

  // 监听 URL 变化的 useEffect
  useEffect(() => {
    const handleUrlChange = () => {
      const newPath = window.location.pathname;
      if (newPath !== currentPath) {
        setCurrentPath(newPath);
      }
    };

    // 监听浏览器前进后退
    window.addEventListener('popstate', handleUrlChange);

    // 监听 SPA 路由变化
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function (...args) {
      originalPushState.apply(history, args);
      setTimeout(handleUrlChange, 0); // 异步执行，确保 URL 已更新
    };

    history.replaceState = function (...args) {
      originalReplaceState.apply(history, args);
      setTimeout(handleUrlChange, 0);
    };

    return () => {
      window.removeEventListener('popstate', handleUrlChange);
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
    };
  }, [currentPath]);

  // 简化的 DOM 监听 useEffect (只作为备用，处理动态内容加载)
  useEffect(() => {
    if (!mounted) return;

    // 检查是否是用户主页
    const isUserProfilePage = (path: string) => {
      return /^\/[^\/]+\/?$/.test(path) &&
        !path.includes('/status/') &&
        !path.includes('/photo/') &&
        !path.includes('/followers') &&
        !path.includes('/following');
    };

    if (!isUserProfilePage(currentPath)) {
      return; // 不在用户主页，不需要监听
    }

    // 初始尝试插入
    insertExportButton();

    // 监听特定区域的 DOM 变化，减少触发频率
    let timeoutId: NodeJS.Timeout | null = null;
    const observer = new MutationObserver((mutations) => {
      // 只在相关区域发生变化时才处理
      const hasRelevantChange = mutations.some(mutation => {
        const target = mutation.target as Element;
        return target.closest('[data-testid="UserName"]') ||
          target.querySelector('[data-testid="primaryColumn"]');
      });
      if (!hasRelevantChange) {
        return;
      }

      // 清除之前的定时器
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // 设置新的定时器，延迟执行
      timeoutId = setTimeout(() => {
        insertExportButton();
      }, 300); // 减少延迟时间
    });

    // 只监听主要UserName内容区域，而不是整个 body
    const primaryColumn = document.querySelector('[data-testid="UserName"]');

    if (primaryColumn) {
      observer.observe(primaryColumn, {
        childList: true,
        subtree: true
      });
    } else {
      // 如果找不到主要区域，则监听 body，但使用更严格的过滤
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    return () => {
      observer.disconnect();
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [mounted, currentPath]); // 依赖 currentPath，当路径变化时重新设置监听

  return null; // 这个组件不渲染任何内容，它只是用来插入按钮
}