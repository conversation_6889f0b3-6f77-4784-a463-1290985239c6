import type { PlasmoCSConfig, PlasmoCSUIProps, PlasmoGetInlineAnchorList, PlasmoMountShadowHost, } from "plasmo"
import { throttle } from "lodash-es"
import { useEffect, useMemo, useRef, useState, useCallback, type FC } from "react"
import { sendToBackground } from "@plasmohq/messaging"
import type { TweetApiUtilsData } from 'twitter-openapi-typescript';
import { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuPortal, DropdownMenuSeparator, DropdownMenuShortcut, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger, DropdownMenuTrigger } from "~components/ui/dropdown-menu";
import styled from "@emotion/styled"
import { Icon } from "@iconify/react";
import { minBy } from 'lodash-es'

import cssText from "data-text:~/globals.css"

import { getDownloadTweetByTweetId } from "~lib/db";
import { cn, extractFilenameFromUrl, formatVideoSize } from "~lib/utils";

// 类型定义
interface VideoVariant {
  bitrate?: number;
  contentType?: string;
  content_type?: string;
  url: string;
}

interface VideoInfo {
  variants: VideoVariant[];
  duration_millis?: number;
  durationMillis?: number;
}

interface MediaEntity {
  type: 'photo' | 'video' | 'animated_gif';
  mediaUrlHttps: string;
  videoInfo?: VideoInfo;
}

interface StyledProps {
  bgImage?: string;
}

// 统一的媒体渲染组件
const MediaItem: FC<{
  item: MediaEntity;
  idx: number;
  onDownload: (url: string) => void;
  prefix?: string;
}> = ({ item, idx, onDownload, prefix = "Media" }) => {
  return (
    <div key={`${prefix}-${idx}`}>
      <DropdownMenuLabel style={{ color: '#63686c', fontFamily: 'math', marginBottom: '10px' }}>
        {prefix} {idx + 1}
        {item.type === 'video' && (
          <div style={{ position: 'relative', height: '55px' }}>
            <StyledVideo
              poster={item.mediaUrlHttps}
              src={minBy(item.videoInfo?.variants?.filter(v => v.bitrate), 'bitrate')?.url}
              muted
              autoPlay
              playsInline
              loop
            />
          </div>
        )}
      </DropdownMenuLabel>

      {item.type === 'photo' && (
        <DropdownMenuGroup>
          <StyledDropdownMenuItem
            onClick={() => onDownload(item.mediaUrlHttps)}
            bgImage={item.mediaUrlHttps}
          >
            {item.type} {idx + 1}
          </StyledDropdownMenuItem>
        </DropdownMenuGroup>
      )}

      {item.type === 'video' && (
        <DropdownMenuGroup>
          {item.videoInfo?.variants?.filter(v => v.bitrate)?.map((video, videoIdx) => (
            <StyledDropdownMenuItem
              key={`${prefix}-video-${videoIdx}`}
              onClick={() => onDownload(video.url)}
            >
              {formatVideoSize(video.bitrate!, item.videoInfo?.duration_millis || item.videoInfo?.durationMillis)}
              <DropdownMenuShortcut style={{ fontWeight: 'lighter', fontSize: 12, opacity: '0.7' }}>
                {video.content_type || video.contentType}
              </DropdownMenuShortcut>
            </StyledDropdownMenuItem>
          ))}
        </DropdownMenuGroup>
      )}

      {item.type === 'animated_gif' && (
        <DropdownMenuGroup>
          {item.videoInfo?.variants?.filter(v => v.url)?.map((gif, gifIdx) => (
            <StyledDropdownMenuItem
              key={`${prefix}-gif-${gifIdx}`}
              onClick={() => onDownload(gif.url)}
            >
              Gif
              <DropdownMenuShortcut style={{ fontWeight: 'lighter', fontSize: 12, opacity: '0.7' }}>
                {gif.content_type || gif.contentType}
              </DropdownMenuShortcut>
            </StyledDropdownMenuItem>
          ))}
        </DropdownMenuGroup>
      )}

      <DropdownMenuSeparator style={{ border: '1px solid #2d33384e', margin: '5px 0' }} />
    </div>
  );
};


export const getStyle = () => {
  const style = document.createElement("style")
  style.textContent = cssText
  return style
}

export const config: PlasmoCSConfig = {
  matches: ["https://twitter.com/*", "https://x.com/*"],
  all_frames: false,
  run_at: "document_idle",
}

export const mountShadowHost: PlasmoMountShadowHost = ({
  shadowHost,
  anchor,
  mountState
}) => {
  anchor.element.appendChild(shadowHost)
  mountState.observer.disconnect()
}

export const getShadowHostId = () => "tgx-shadow-host-" + Math.random().toString(36).slice(2)

export const getInlineAnchorList: PlasmoGetInlineAnchorList = throttle(async () => {
  const anchors = Array.from(document.querySelectorAll('article [role="group"]:not([data-button-inserted])'))

  return anchors.map((element) => ({
    element,
    insertPosition: "beforeend" as const
  }))
}, 1000)


const StyledDropdownMenuContent = styled(DropdownMenuContent)`
  min-width: 200px;
  padding: 10px;
  background-color: #c0c0c033; /* 半透明白色背景 */
  backdrop-filter: blur(12px);                 /* 毛玻璃模糊效果 */
  -webkit-backdrop-filter: blur(12px);         /* Safari 支持 */
  border-radius: 12px;                          /* 圆角（可选） */
  border: 1px solid rgba(255, 255, 255, 0.1);  /* 半透明边框（可选） */
  @media (prefers-color-scheme: dark) {
    background-color: #01010133;
  }
`
const StyledDropdownMenuItem = styled(DropdownMenuItem) <StyledProps>`
  position: relative;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: monospace;
  cursor: pointer;
  transition: all .1s ease-in-out;
  outline: none;
  position: relative;
  z-index: 0;
  ${({ bgImage }) =>
    bgImage &&
    `
      background-image: url(${bgImage});
      background-repeat: no-repeat;
      background-position: right center;
      background-size: contain;
    `}
  &:hover {
    background-color: #1d9cf038;
    color: #1d9bf0;
  }
`

const StyledVideo = styled.video`
  width: 100%;
  height: 55px;
  border-radius: 14px;
  object-fit: cover;
  transition: all 0.25s ease-in-out 0.2s;
  z-index: 1000;
  position: absolute;
  bottom: 0;
  &:hover {
    width: 120%;
    height: 100px;
    position: absolute;
    bottom: 36px;
    transform: scale(1.7);
    border-radius: 6px;
  }
`

const Button: FC<PlasmoCSUIProps> = ({ anchor }) => {
  const btnRef = useRef<HTMLDivElement>()

  const [data, setData] = useState<TweetApiUtilsData>()
  const tweet = data?.tweet

  const isDetailPage = useMemo(() => {
    return window.location.pathname.includes('/status/')
  }, [window.location.pathname])

  const media = useMemo(() => {
    if (!tweet?.legacy) return [];

    const media = tweet.legacy?.extendedEntities?.media
    return media
  }, [tweet])

  const cardMedia = useMemo(() => {
    if (!tweet?.card?.legacy) return []

    const res = tweet.card.legacy.bindingValues.find(i => i.key === "unified_card")
    if (!res || !res?.value?.stringValue) return []

    const card = JSON.parse(res.value.stringValue)

    const mediaEntities = card?.media_entities || {}
    return Object.values(mediaEntities).map((entity: any): MediaEntity => ({
      type: entity.type || 'photo',
      mediaUrlHttps: entity.media_url_https,
      videoInfo: entity.video_info
    }))
  }, [tweet?.card])

  const handleGetTweet = useCallback(async (tweetId: string) => {
    const res = await getDownloadTweetByTweetId(tweetId)
    setData(res?.data)
  }, [])

  useEffect(() => {
    if (anchor) {
      const anchorElement = (anchor as any).root._internalRoot?.containerInfo as Element | null;
      const shadowRoot = anchorElement.getRootNode() as ShadowRoot
      const el = shadowRoot.host.parentNode as Element
      el.setAttribute('data-button-inserted', 'true')

      let tweetId: string | null = null

      const tweetLink = el.querySelector('a[href*="/status/"]') as HTMLAnchorElement;
      if (tweetLink) {
        const match = tweetLink.href.match(/\/status\/(\d+)/);
        if (match) {
          tweetId = match[1]
        }
      }

      if (!tweetId && window.location.pathname.includes('/status/')) {
        const urlMatch = window.location.pathname.match(/\/status\/(\d+)/);
        if (urlMatch) {
          tweetId = urlMatch[1]
        }
      }

      if (tweetId) {
        handleGetTweet(tweetId)
      }
    }
  }, [anchor, handleGetTweet]);

  const handleDownload = useCallback(async (url: string) => {
    try {
      const res = await sendToBackground({
        name: "download-twitter",
        body: {
          url,
          filename: extractFilenameFromUrl(url)
        }
      })

      if (res?.error) {
        console.error('下载失败:', res.error);
        alert('下载失败: ' + res.error);
      } else if (res?.success) {
        console.log('下载成功，ID:', res.downloadId);
      }
    } catch (error: any) {
      console.error('下载请求失败:', error);
      alert('下载请求失败: ' + (error?.message || '未知错误'));
    }
  }, [])


  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger
        className={cn(
          "relative w-[18.75px] h-[18.75px] rounded-full cursor-pointer justify-center text-[#71767b] hover:text-[rgba(29,155,240)] hover:bg-[rgba(29,155,240,0.1)] ml-4",
        )}
        style={{
          display: !media?.length && !cardMedia?.length ? "none" : "inline-flex",
        }}
      >
        <div
          role="dl-btn"
          ref={btnRef}
          className="w-4.5 h-4.5 inline-flex justify-center items-center cursor-pointer relative"
          onClick={(e) => {
            e.stopPropagation()
            e.preventDefault()
          }}
        >
          <div className="absolute top-1/2 -translate-y-1/2 w-9 h-9 hover:bg-[rgba(29,155,240,0.1)] rounded-full" />
          <Icon icon="ooui:download" fontSize={17} />
        </div>
      </DropdownMenuTrigger>
      <StyledDropdownMenuContent align="start">
        {/* Regular Media */}
        {media?.map((item, idx) => (
          <MediaItem
            key={`media-${idx}`}
            item={item as unknown as MediaEntity}
            idx={idx}
            onDownload={handleDownload}
            prefix="Media"
          />
        ))}

        {/* Card Media */}
        {cardMedia?.map((item, idx) => (
          <MediaItem
            key={`card-${idx}`}
            item={item}
            idx={idx}
            onDownload={handleDownload}
            prefix="Card Media"
          />
        ))}

        {/* 一键下载所有媒体 */}
        {/* {media && media.length > 1 && (
          <>
            <DropdownMenuSeparator style={{ border: '1px solid #3336396b', margin: '10px 0' }} />
            <DropdownMenuGroup>
              <StyledDropdownMenuItem onClick={handleDownloadAll}>
                📥 一键下载全部
                <DropdownMenuShortcut style={{ fontWeight: 'lighter', fontSize: 12, opacity: '0.7' }}>
                  {media.length} 个文件
                </DropdownMenuShortcut>
              </StyledDropdownMenuItem>
            </DropdownMenuGroup>
          </>
        )} */}
      </StyledDropdownMenuContent>
    </DropdownMenu>
  )
}


export default Button