import type { PlasmoCSConfig, PlasmoMountShadowHost } from "plasmo"
import { useMessage } from "@plasmohq/messaging/hook"

export const config: PlasmoCSConfig = {
  all_frames: true,
  matches: ["<all_urls>"]
}

const QueryTextAnywhere = () => {
  // const { data } = useMessage<string, string>(async (req, res) => {
  //   res.send(document.querySelector(req.body).textContent)
  // })
  return (
    <div
      style={{
        padding: 8,
        background: "#333",
        color: "red",
        display: "none"
      }}>
      Querying Selector for:
    </div>
  )
}

export default QueryTextAnywhere
