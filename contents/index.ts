import type { PlasmoCSConfig } from "plasmo"

import { sendToBackground } from "@plasmohq/messaging"
// import { relay } from "@plasmohq/messaging/relay"
import { TimelineFromJSON } from "twitter-openapi-typescript-generated";
import { entriesCursor, instructionConverter, instructionToEntry, tweetEntriesConverter } from "twitter-openapi-typescript";
import { addOrUpdateDownloadTweet, db } from "~lib/db";

export const config: PlasmoCSConfig = {
  matches: ["https://twitter.com/*", "https://x.com/*"],
  all_frames: false,
  run_at: "document_end",
  world: "MAIN"
}

// relayMessage({
//   name: "get-manifest"
// })

// relay(
//   {
//     name: "math/add" as const
//   },
//   async (req) => {
//     const { a, b } = req.body
//     const minusResult = a - b - 9

//     document.getElementById(
//       "subtract-result"
//     ).innerText = `${a} minus ${b} is ${minusResult}`

//     const addResult = await sendToBackground(req)
//     console.log('%c [ addResult ]-199', 'font-size:13px; background:pink; color:#bf2c9f;', addResult)
//     return addResult
//   }
// )

// 获取 cookies 的函数
// async function getCookies() {
//   try {
//     const response = await sendToBackground({
//       name: "get-cookies", // 这个名字需要和你的 handler 文件名匹配
//       body: {}
//     })
//     console.log('%c [ Twitter Cookies ]-40', 'font-size:13px; background:pink; color:#bf2c9f;', response.cookies)
//     return response.cookies
//   } catch (error) {
//     console.error('获取 cookies 失败:', error)
//     return null
//   }
// }

function handleHomeTimeline(responseData: any) {
  const homeTimeline = TimelineFromJSON(responseData.data.home.home_timeline_urt)
  const entry = instructionToEntry(homeTimeline.instructions);

  // 得到 tweet 数据和其他数据
  const tweetData = tweetEntriesConverter(entry);

  const instructionData = instructionConverter(homeTimeline.instructions);

  // 合并结果
  const finalData = [...tweetData, ...instructionData];
  // 获取 cursor
  // const cursor = entriesCursor(entry);
  finalData.forEach(item => {
    const tweetId = item.tweet.restId;
    db.downloadTweets.add({
      tweetId,
      data: item
    })
    addOrUpdateDownloadTweet(tweetId, item)
  })
}

function handleTweetDetail(responseData: any) {
  try {
    const tweetDetail = TimelineFromJSON(responseData.data.threaded_conversation_with_injections_v2)
    const entry = instructionToEntry(tweetDetail.instructions);

    // 得到 tweet 数据和其他数据
    const tweetData = tweetEntriesConverter(entry);
    const instructionData = instructionConverter(tweetDetail.instructions);

    // 合并结果
    const finalData = [...tweetData, ...instructionData];
    finalData.forEach(item => {
      const tweetId = item.tweet.restId;
      db.downloadTweets.add({
        tweetId,
        data: item
      })
      addOrUpdateDownloadTweet(tweetId, item)
    })
  } catch (e) {
    console.error('处理 TweetDetail 失败:', e);
  }
}

function handleUserTweets(responseData: any) {
  try {
    const userTweets = TimelineFromJSON(
      responseData.data.user.result?.timeline_v2?.timeline ||
      responseData.data.user.result?.timeline?.timeline
    )
    const entry = instructionToEntry(userTweets.instructions);

    // 得到 tweet 数据和其他数据
    const tweetData = tweetEntriesConverter(entry);
    const instructionData = instructionConverter(userTweets.instructions);

    // 合并结果
    const finalData = [...tweetData, ...instructionData];
    finalData.forEach(item => {
      const tweetId = item.tweet.restId;
      db.downloadTweets.add({
        tweetId,
        data: item
      })
      addOrUpdateDownloadTweet(tweetId, item)
    })
  } catch (e) {
    console.error('处理 UserTweets 失败:', e);
  }
}

function handleSearchTimeline(responseData: any) {
  try {
    const searchTimeline = TimelineFromJSON(responseData.data.search_by_raw_query.search_timeline.timeline)
    const entry = instructionToEntry(searchTimeline.instructions);

    // 得到 tweet 数据和其他数据
    const tweetData = tweetEntriesConverter(entry);
    const instructionData = instructionConverter(searchTimeline.instructions);

    // 合并结果
    const finalData = [...tweetData, ...instructionData];
    finalData.forEach(item => {
      const tweetId = item.tweet.restId;
      db.downloadTweets.add({
        tweetId,
        data: item
      })
      addOrUpdateDownloadTweet(tweetId, item)
    })
  } catch (e) {
    console.error('处理 SearchTimeline 失败:', e);
  }
}

function interceptXHR() {
  const originalOpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function (method, url) {
    const interceptedPaths = [
      '/HomeTimeline',
      '/TweetDetail',
      '/UserTweets',
      '/Likes',
      '/SearchTimeline',
    ]

    this._isIntercepted = interceptedPaths.some((path) => url.includes(path))

    originalOpen.apply(this, arguments);
  };

  const originalSend = XMLHttpRequest.prototype.send;
  XMLHttpRequest.prototype.send = function () {
    if (this._isIntercepted) {
      this.addEventListener('load', function (e: ProgressEvent) {
        const target = e.target as XMLHttpRequest
        const url = target.responseURL
        console.log('xhr url', url)
        try {
          const responseData = JSON.parse(this.responseText);

          const handlerMap = [
            { keyword: '/HomeTimeline', handler: handleHomeTimeline },
            { keyword: '/TweetDetail', handler: handleTweetDetail },
            { keyword: '/UserTweets', handler: handleUserTweets },
            { keyword: '/Likes', handler: handleUserTweets },
            { keyword: '/SearchTimeline', handler: handleSearchTimeline }
          ]

          const matched = handlerMap.find(({ keyword }) => url.includes(keyword))

          if (matched) {
            matched.handler(responseData)
          }
        } catch (e) {
          console.error('解析 XHR 响应失败:', e);
        }
      });
    }
    originalSend.apply(this, arguments);
  };
}

// 初始化函数
function initialize() {
  console.log("Initializing Twitter download extension...")

  interceptXHR()
  // 等待页面加载完成
  const initDelay = document.readyState === 'loading' ? 3000 : 1500
  setTimeout(() => {
    interceptXHR()
    console.log("Twitter download extension initialized")
  }, initDelay)
}

// 启动扩展
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize)
} else {
  initialize()
}
