import type { PlasmoMessaging } from "@plasmohq/messaging"
import { TimelineFromJSON } from "twitter-openapi-typescript-generated";
import { instructionConverter, instructionToEntry, tweetEntriesConverter } from "twitter-openapi-typescript";

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  const { responseData, endpoint } = req.body;

  try {
    let finalData: any[] = []

    switch (endpoint) {
      case 'HomeTimeline':
        finalData = handleHomeTimeline(responseData)
        break
      case 'TweetDetail':
        finalData = handleTweetDetail(responseData)
        break
      case 'UserTweets':
      case 'Likes':
        finalData = handleUserTweets(responseData)
        break
      case 'SearchTimeline':
        finalData = handleSearchTimeline(responseData)
        break
    }

    res.send({
      success: true,
      data: finalData
    })

  } catch (error: any) {
    console.error('处理 timeline 数据失败:', error)
    res.send({
      success: false,
      error: error.message
    })
  }
}

function handleHomeTimeline(responseData: any) {
  const homeTimeline = TimelineFromJSON(responseData.data.home.home_timeline_urt)
  const entry = instructionToEntry(homeTimeline.instructions);

  // 得到 tweet 数据和其他数据
  const tweetData = tweetEntriesConverter(entry);
  const instructionData = instructionConverter(homeTimeline.instructions);

  // 合并结果
  return [...tweetData, ...instructionData];
}

function handleTweetDetail(responseData: any) {
  try {
    const tweetDetail = TimelineFromJSON(responseData.data.threaded_conversation_with_injections_v2)
    const entry = instructionToEntry(tweetDetail.instructions);

    // 得到 tweet 数据和其他数据
    const tweetData = tweetEntriesConverter(entry);
    const instructionData = instructionConverter(tweetDetail.instructions);

    // 合并结果
    return [...tweetData, ...instructionData];
  } catch (e) {
    console.error('处理 TweetDetail 失败:', e);
    return []
  }
}

function handleUserTweets(responseData: any) {
  try {
    const userTweets = TimelineFromJSON(
      responseData.data.user.result?.timeline_v2?.timeline ||
      responseData.data.user.result?.timeline?.timeline
    )
    const entry = instructionToEntry(userTweets.instructions);

    // 得到 tweet 数据和其他数据
    const tweetData = tweetEntriesConverter(entry);
    const instructionData = instructionConverter(userTweets.instructions);

    // 合并结果
    return [...tweetData, ...instructionData];
  } catch (e) {
    console.error('处理 UserTweets 失败:', e);
    return []
  }
}

function handleSearchTimeline(responseData: any) {
  try {
    const searchTimeline = TimelineFromJSON(responseData.data.search_by_raw_query.search_timeline.timeline)
    const entry = instructionToEntry(searchTimeline.instructions);

    // 得到 tweet 数据和其他数据
    const tweetData = tweetEntriesConverter(entry);
    const instructionData = instructionConverter(searchTimeline.instructions);

    // 合并结果
    return [...tweetData, ...instructionData];
  } catch (e) {
    console.error('处理 SearchTimeline 失败:', e);
    return []
  }
}

export default handler