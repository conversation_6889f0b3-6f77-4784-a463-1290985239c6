// import type { PlasmoMessaging } from "@plasmohq/messaging"
// import { TwitterOpenApi } from 'twitter-openapi-typescript'

// interface ExportRequest {
//   exportType: 'following' | 'followers' | 'verified_followers'
//   userId: string
//   cookieString: string
//   cursor?: string
// }

// const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
//   const { exportType, userId, cookieString, cursor } = req.body as ExportRequest

//   try {
//     // 创建 Twitter API 客户端
//     const api = new TwitterOpenApi();
    
//     // 使用 cookie 字符串初始化客户端
//     const client = await api.getClientFromCookies({
        // ...
//     });

//     let response: any

//     // 根据导出类型调用不同的 API
//     switch (exportType) {
//       case 'following':
//         response = await client.getUserFollowing(userId, cursor)
//         break
//       case 'followers':  
//         response = await client.getUserFollowers(userId, cursor)
//         break
//       case 'verified_followers':
//         response = await client.getUserVerifiedFollowers(userId, cursor)
//         break
//       default:
//         throw new Error(`不支持的导出类型: ${exportType}`)
//     }

//     // 处理响应数据
//     const users = response?.data?.users || []
//     const nextCursor = response?.data?.nextCursor

//     // 转换用户数据格式
//     const processedUsers = users.map((user: any) => ({
//       targetUserId: user.id,
//       username: user.username,
//       displayName: user.displayName,
//       description: user.description,
//       followerCount: user.followerCount,
//       followingCount: user.followingCount,
//       tweetCount: user.tweetCount,
//       verified: user.verified,
//       profileImageUrl: user.profileImageUrl
//     }))

//     res.send({
//       success: true,
//       data: {
//         users: processedUsers,
//         nextCursor,
//         hasMore: !!nextCursor
//       }
//     })

//   } catch (error: any) {
//     console.error('Twitter API 调用失败:', error)
//     res.send({
//       success: false,
//       error: error.message || '未知错误'
//     })
//   }
// }

// // 从 cookie 字符串中提取特定值
// function extractCookieValue(cookieString: string, cookieName: string): string {
//   const match = cookieString.match(new RegExp(`${cookieName}=([^;]+)`))
//   return match ? match[1] : ''
// }

// export default handler