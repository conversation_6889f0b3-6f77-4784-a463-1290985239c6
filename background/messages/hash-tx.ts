import type { PlasmoMessaging } from "@plasmohq/messaging"

const HIDDEN_NUMBER = 541

export type RequestBody = {
  input: number
}

export type RequestResponse = number

const handler: PlasmoMessaging.MessageHandler<
  RequestBody,
  RequestResponse
> = async (req, res) => {
  const { input } = req.body
  console.log('%c [ input ]-16', 'font-size:13px; background:pink; color:#bf2c9f;', input)

  res.send(input * HIDDEN_NUMBER)
}

export default handler
