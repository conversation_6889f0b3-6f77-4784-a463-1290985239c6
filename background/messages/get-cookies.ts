import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  try {
    const domain = req.body?.domain || "x.com";
    const cookies = await chrome.cookies.getAll({ domain });
    
    const cookieString = cookies
      .map(cookie => `${cookie.name}=${cookie.value}`)
      .join('; ');
    
    const cookieObj = cookies.reduce((obj, cookie) => {
      obj[cookie.name] = cookie.value;
      return obj;
    }, {} as Record<string, string>);

    console.log('%c [ Background: Got cookies ]-10', 'font-size:13px; background:green; color:white;', cookies.length, 'cookies');
    
    res.send({ 
      cookies,
      cookieString,
      cookieObj,
      success: true 
    });
  } catch (error: any) {
    console.error('获取 cookies 失败:', error)
    res.send({ 
      cookies: [], 
      cookieString: '',
      cookieObj: null,
      error: error.message,
      success: false 
    });
  }
}

export default handler
