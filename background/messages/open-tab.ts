import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  try {
    if (req.body.type === 'openTab' && req.body.url) {
      const tab = await chrome.tabs.create({
        url: req.body.url,
        active: true
      });
      
      res.send({ 
        success: true, 
        tabId: tab.id 
      });
    } else {
      res.send({ 
        success: false, 
        error: 'Invalid request' 
      });
    }
  } catch (error: any) {
    console.error('打开标签页失败:', error);
    res.send({ 
      success: false, 
      error: error.message 
    });
  }
}

export default handler