import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  try {
    const { url, filename } = req.body;

    if (!url) {
      res.send({ error: "URL is required" });
      return;
    }

    console.log('开始下载:', { url, filename });

    const downloadId = await chrome.downloads.download({
      url: url,
      filename: filename || 'download',
      saveAs: false // 自动保存到默认下载文件夹
    });

    console.log('下载成功，ID:', downloadId);
    res.send({ success: true, downloadId });
  } catch (error) {
    console.error('下载失败:', error);
    res.send({ error: error.message || '下载失败' });
  }
}

export default handler