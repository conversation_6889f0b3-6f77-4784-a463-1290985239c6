{"name": "test-msg", "displayName": "Test msg", "version": "0.0.1", "description": "A basic Plasmo extension.", "author": "Plasmo Corp. <<EMAIL>>", "scripts": {"dev": "run-p dev:*", "dev:plasmo": "plasmo dev", "build": "run-s build:*", "build:plasmo": "plasmo build", "package": "plasmo package"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@heroui/react": "^2.7.11", "@heroui/system": "^2.4.18", "@heroui/theme": "^2.4.17", "@iconify/react": "^6.0.0", "@plasmohq/messaging": "0.7.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "framer-motion": "^12.23.0", "lodash-es": "^4.17.21", "lucide-react": "^0.525.0", "papaparse": "^5.5.3", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.5", "twitter-api-v2": "^1.24.0", "twitter-openapi-typescript": "^0.0.55", "twitter-openapi-typescript-generated": "0.0.37"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@plasmohq/rps": "1.8.7", "@types/chrome": "0.0.258", "@types/lodash-es": "^4.17.12", "@types/node": "20.11.5", "@types/papaparse": "^5.3.16", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "prettier": "3.2.4", "tailwindcss": "^3.4.17", "typescript": "5.3.3"}, "manifest": {"permissions": ["activeTab", "tabs", "cookies", "downloads"], "host_permissions": ["https://*/*", "http://*/*"], "externally_connectable": {"matches": ["*://*.localhost.com/*"]}}}