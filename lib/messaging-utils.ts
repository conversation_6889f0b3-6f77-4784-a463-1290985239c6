import { sendToBackground } from "@plasmohq/messaging"
import React from "react"

/**
 * 检查扩展上下文是否有效
 */
export function isExtensionContextValid(): boolean {
  try {
    // 尝试访问 chrome.runtime
    return !!(chrome?.runtime?.id);
  } catch (error) {
    return false;
  }
}

/**
 * 带重试机制和错误处理的消息发送函数
 */
export async function sendMessageWithRetry(
  message: any, 
  options: {
    maxRetries?: number;
    retryDelay?: number;
    onRetry?: (attempt: number, error: Error) => void;
    onContextInvalidated?: () => void;
  } = {}
) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    onRetry,
    onContextInvalidated
  } = options;

  // 首先检查扩展上下文
  if (!isExtensionContextValid()) {
    const error = new Error('Extension context is not available');
    onContextInvalidated?.();
    throw error;
  }

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await sendToBackground(message);
      return response;
    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      
      console.warn(`消息发送失败 (尝试 ${attempt}/${maxRetries}):`, errorMessage);
      
      // 调用重试回调
      onRetry?.(attempt, error);
      
      if (errorMessage.includes('Extension context invalidated') || 
          errorMessage.includes('context invalidated') ||
          !isExtensionContextValid()) {
        
        if (attempt < maxRetries) {
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
          continue;
        } else {
          // 最后一次尝试失败，调用上下文失效回调
          onContextInvalidated?.();
          throw new Error('扩展上下文失效，请刷新页面重试');
        }
      } else {
        // 其他类型的错误，直接抛出
        throw error;
      }
    }
  }
}

/**
 * 扩展上下文恢复策略
 */
export class ExtensionContextManager {
  private static instance: ExtensionContextManager;
  private contextCheckInterval: NodeJS.Timeout | null = null;
  private listeners: Array<() => void> = [];

  static getInstance(): ExtensionContextManager {
    if (!ExtensionContextManager.instance) {
      ExtensionContextManager.instance = new ExtensionContextManager();
    }
    return ExtensionContextManager.instance;
  }

  /**
   * 开始监听扩展上下文状态
   */
  startMonitoring(intervalMs: number = 5000) {
    if (this.contextCheckInterval) {
      clearInterval(this.contextCheckInterval);
    }

    this.contextCheckInterval = setInterval(() => {
      if (!isExtensionContextValid()) {
        console.warn('检测到扩展上下文失效');
        this.notifyListeners();
      }
    }, intervalMs);
  }

  /**
   * 停止监听
   */
  stopMonitoring() {
    if (this.contextCheckInterval) {
      clearInterval(this.contextCheckInterval);
      this.contextCheckInterval = null;
    }
  }

  /**
   * 添加上下文失效监听器
   */
  addContextInvalidatedListener(callback: () => void) {
    this.listeners.push(callback);
  }

  /**
   * 移除监听器
   */
  removeContextInvalidatedListener(callback: () => void) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('上下文失效监听器执行失败:', error);
      }
    });
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.stopMonitoring();
    this.listeners = [];
  }
}

/**
 * 显示用户友好的错误提示
 */
export function showExtensionError(error: Error, context: string = '') {
  const errorMessage = error.message || error.toString();
  
  if (errorMessage.includes('Extension context invalidated') || 
      errorMessage.includes('context invalidated')) {
    
    console.error(`${context} 扩展上下文失效:`, error);
    
    // 可以在这里添加用户通知，比如显示一个提示框
    if (typeof window !== 'undefined') {
      const message = '扩展需要重新加载。请刷新页面或重新加载扩展。';
      
      // 尝试显示原生提示
      if (confirm(`${message}\n\n点击确定刷新页面？`)) {
        window.location.reload();
      }
    }
  } else {
    console.error(`${context} 发生错误:`, error);
  }
}

/**
 * React Hook: 使用扩展上下文管理
 */
export function useExtensionContext() {
  const [isValid, setIsValid] = React.useState(isExtensionContextValid());
  
  React.useEffect(() => {
    const manager = ExtensionContextManager.getInstance();
    
    const handleContextInvalidated = () => {
      setIsValid(false);
    };
    
    manager.addContextInvalidatedListener(handleContextInvalidated);
    manager.startMonitoring();
    
    return () => {
      manager.removeContextInvalidatedListener(handleContextInvalidated);
      manager.stopMonitoring();
    };
  }, []);
  
  return {
    isValid,
    checkContext: isExtensionContextValid,
    sendMessage: sendMessageWithRetry
  };
}
