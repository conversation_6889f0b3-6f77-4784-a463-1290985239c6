import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HeroUIProvider } from "@heroui/react";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
})

export const Providers = ({ children }) => {
  return <>
    <QueryClientProvider client={queryClient}>
      <HeroUIProvider className="h-full">
        {children}
      </HeroUIProvider>
    </QueryClientProvider>
  </>
}