import Dexie, { type EntityTable } from 'dexie';
import type { TweetApiUtilsData } from 'twitter-openapi-typescript';
import type { User, Tweet } from 'twitter-openapi-typescript-generated';

// ==================== 数据类型定义 ====================

// 用户基础信息（简化，主要用于建立关联关系）
export interface TwitterUser {
  id: string; // Twitter 用户 ID（主键）
  data: User; // 完整的用户原始数据
  
  // 导出元数据
  lastUpdated: number; // 最后更新时间戳
  exportSource: string; // 导出来源（哪个用户导出的）
}

// 用户关注者表（只存储关系和原始数据）
export interface UserFollowers {
  id?: number; // 自增主键
  targetUserId: string; // 被关注的用户 Twitter ID（与 users 表关联）
  followerUserId: string; // 关注者的 Twitter ID（与 users 表关联）
  
  // 关注者完整原始数据
  data: User; // 关注者的完整原始数据
  
  // 关注关系元数据
  followedAt?: string; // 关注时间（如果API提供）
  exportTime: number; // 导出时间戳
  exportBatchId: string; // 导出批次ID
}

// 用户关注列表表（只存储关系和原始数据）
export interface UserFollowing {
  id?: number; // 自增主键
  sourceUserId: string; // 关注者的 Twitter ID（与 users 表关联）
  followingUserId: string; // 被关注者的 Twitter ID（与 users 表关联）
  
  // 被关注者完整原始数据
  data: User; // 被关注者的完整原始数据
  
  // 关注关系元数据
  followedAt?: string; // 关注时间（如果API提供）
  exportTime: number; // 导出时间戳
  exportBatchId: string; // 导出批次ID
}

// 用户推文表（只存储关系和原始数据）
export interface UserTweets {
  id: string; // 推文 Twitter ID（主键）
  authorUserId: string; // 作者的 Twitter ID（与 users 表关联）
  
  // 完整的推文和作者原始数据
  tweetData: Tweet; // 完整的推文原始数据
  authorData: User; // 完整的作者原始数据
  
  // 导出元数据
  exportTime: number; // 导出时间戳
}



// 临时下载推文数据（保持向后兼容）
interface DownloadTweet {
  id: number;
  tweetId: string;
  data: TweetApiUtilsData;
}

// ==================== 统一数据库定义 ====================

class UnifiedDB extends Dexie {
  // 临时下载数据表（保持向后兼容）
  downloadTweets!: EntityTable<DownloadTweet, 'id'>;

  // Twitter 导出功能表
  users!: EntityTable<TwitterUser, 'id'>;
  userFollowers!: EntityTable<UserFollowers, 'id'>;
  userFollowing!: EntityTable<UserFollowing, 'id'>;
  userTweets!: EntityTable<UserTweets, 'id'>;

  constructor() {
    super('TgxDB'); // 保持原有数据库名

    this.version(1).stores({
      // 临时下载数据（保持向后兼容）
      downloadTweets: "++id, tweetId, data"
    });

    // 版本2：添加 Twitter 导出功能表（简化版）
    this.version(2).stores({
      // 保持原有表
      downloadTweets: "++id, tweetId, data",

      // 新增 Twitter 导出表（简化版）
      users: 'id, lastUpdated, exportSource', // 主键是 id，也可按 lastUpdated 和 exportSource 查询
      userFollowers: '++id, targetUserId, followerUserId, exportTime, exportBatchId, [targetUserId+followerUserId]',
      userFollowing: '++id, sourceUserId, followingUserId, exportTime, exportBatchId, [sourceUserId+followingUserId]', 
      userTweets: 'id, authorUserId, exportTime' // 主键是 id，也可按 authorUserId 和 exportTime 查询
    });
  }
}

export const db = new UnifiedDB();


export async function getDownloadTweetByTweetId(tweetId: string): Promise<DownloadTweet | undefined> {
  return await db.downloadTweets.where('tweetId').equals(tweetId).first();
}

export async function deleteDownloadTweetByTweetId(tweetId: string): Promise<number> {
  return await db.downloadTweets.where('tweetId').equals(tweetId).delete();
}

/**
 * 将下载推文数据添加到数据库。
 * 如果该 tweetId 的数据已存在，则会更新现有条目；否则，会添加新条目。
 * @param tweetId 推文的唯一 ID。
 * @param data 与推文相关的 Twitter API 数据。
 * @returns 返回添加或更新的 DownloadTweet 对象的 id。
 */
export async function addOrUpdateDownloadTweet(tweetId: string, data: TweetApiUtilsData): Promise<number> {
  // 首先尝试查找是否已存在该 tweetId 的记录
  const existingTweet = await getDownloadTweetByTweetId(tweetId);

  if (existingTweet) {
    // 如果存在，更新数据
    await db.downloadTweets.update(existingTweet.id, { data });
    return existingTweet.id;
  } else {
    // 如果不存在，添加新数据
    const id = await db.downloadTweets.add({ tweetId, data });
    return id;
  }
}

// ==================== Twitter 导出功能操作函数 ====================

// 用户相关操作
export async function saveUser(user: TwitterUser): Promise<string> {
  user.lastUpdated = Date.now();
  await db.users.put(user);
  return user.id;
}

export async function getUser(userId: string): Promise<TwitterUser | undefined> {
  return await db.users.get(userId);
}

export async function getUserByUsername(username: string): Promise<TwitterUser | undefined> {
  // 由于简化后的设计，需要遍历所有用户找到匹配的用户名
  const users = await db.users.toArray();
  return users.find(user => user.data?.legacy?.screenName === username);
}

// ==================== 用户关注者相关操作 ====================

// 保存用户的关注者列表
export async function saveUserFollowers(followers: UserFollowers[]): Promise<void> {
  await db.userFollowers.bulkPut(followers);
}

// 获取用户的关注者
export async function getUserFollowers(userId: string): Promise<UserFollowers[]> {
  return await db.userFollowers
    .where('targetUserId')
    .equals(userId)
    .toArray();
}

// 获取用户的认证关注者
export async function getUserVerifiedFollowers(userId: string): Promise<UserFollowers[]> {
  const followers = await db.userFollowers
    .where('targetUserId')
    .equals(userId)
    .toArray();
  
  return followers.filter(follower => follower.data?.legacy?.verified === true);
}

// 按关注者数量排序获取关注者
export async function getUserFollowersSortedByFollowersCount(userId: string, limit?: number): Promise<UserFollowers[]> {
  const followers = await db.userFollowers
    .where('targetUserId')
    .equals(userId)
    .toArray();

  const sortedFollowers = followers.sort((a, b) => {
    const aCount = a.data?.legacy?.followersCount || 0;
    const bCount = b.data?.legacy?.followersCount || 0;
    return bCount - aCount;
  });

  return limit ? sortedFollowers.slice(0, limit) : sortedFollowers;
}

// ==================== 用户关注列表相关操作 ====================

// 保存用户的关注列表
export async function saveUserFollowing(following: UserFollowing[]): Promise<void> {
  await db.userFollowing.bulkPut(following);
}

// 获取用户关注的人
export async function getUserFollowing(userId: string): Promise<UserFollowing[]> {
  return await db.userFollowing
    .where('sourceUserId')
    .equals(userId)
    .toArray();
}

// 获取用户关注的认证用户
export async function getUserVerifiedFollowing(userId: string): Promise<UserFollowing[]> {
  const following = await db.userFollowing
    .where('sourceUserId')
    .equals(userId)
    .toArray();
    
  return following.filter(follow => follow.data?.legacy?.verified === true);
}

// 按关注者数量排序获取关注列表
export async function getUserFollowingSortedByFollowersCount(userId: string, limit?: number): Promise<UserFollowing[]> {
  const following = await db.userFollowing
    .where('sourceUserId')
    .equals(userId)
    .toArray();

  const sortedFollowing = following.sort((a, b) => {
    const aCount = a.data?.legacy?.followersCount || 0;
    const bCount = b.data?.legacy?.followersCount || 0;
    return bCount - aCount;
  });

  return limit ? sortedFollowing.slice(0, limit) : sortedFollowing;
}

// ==================== 批量操作函数 ====================

// 批量添加关注者
export async function addUserFollower(
  targetUserId: string,
  followerUserId: string,
  followerRawData: User,
  exportBatchId: string,
  followedAt?: string
): Promise<void> {
  const follower: UserFollowers = {
    targetUserId: targetUserId,
    followerUserId: followerUserId,
    data: followerRawData,
    followedAt: followedAt,
    exportTime: Date.now(),
    exportBatchId: exportBatchId
  };

  await db.userFollowers.add(follower);
}

// 批量添加关注的人
export async function addUserFollowing(
  sourceUserId: string,
  followingUserId: string,
  followingRawData: User,
  exportBatchId: string,
  followedAt?: string
): Promise<void> {
  const following: UserFollowing = {
    sourceUserId: sourceUserId,
    followingUserId: followingUserId,
    data: followingRawData,
    followedAt: followedAt,
    exportTime: Date.now(),
    exportBatchId: exportBatchId
  };

  await db.userFollowing.add(following);
}

// ==================== 用户推文相关操作 ====================

// 保存用户推文
export async function saveUserTweets(tweets: UserTweets[]): Promise<void> {
  await db.userTweets.bulkPut(tweets);
}

// 获取用户的推文
export async function getUserTweets(userId: string): Promise<UserTweets[]> {
  return await db.userTweets
    .where('authorUserId')
    .equals(userId)
    .reverse()
    .sortBy('exportTime');
}

// 获取用户的原创推文（不包括转推和回复）
export async function getUserOriginalTweets(userId: string): Promise<UserTweets[]> {
  const tweets = await db.userTweets
    .where('authorUserId')
    .equals(userId)
    .toArray();
    
  return tweets.filter(tweet => {
    const retweetedStatusResult = tweet.tweetData?.legacy?.retweetedStatusResult;
    const inReplyToStatusIdStr = tweet.tweetData?.legacy?.inReplyToStatusIdStr;
    return !retweetedStatusResult && !inReplyToStatusIdStr;
  }).sort((a, b) => {
    const aTime = new Date(a.tweetData?.legacy?.createdAt || 0).getTime();
    const bTime = new Date(b.tweetData?.legacy?.createdAt || 0).getTime();
    return bTime - aTime;
  });
}

// 获取用户的转推
export async function getUserRetweets(userId: string): Promise<UserTweets[]> {
  const tweets = await db.userTweets
    .where('authorUserId')
    .equals(userId)
    .toArray();
    
  return tweets.filter(tweet => {
    return tweet.tweetData?.legacy?.retweetedStatusResult != null;
  }).sort((a, b) => {
    const aTime = new Date(a.tweetData?.legacy?.createdAt || 0).getTime();
    const bTime = new Date(b.tweetData?.legacy?.createdAt || 0).getTime();
    return bTime - aTime;
  });
}

// 获取用户的回复
export async function getUserReplies(userId: string): Promise<UserTweets[]> {
  const tweets = await db.userTweets
    .where('authorUserId')
    .equals(userId)
    .toArray();
    
  return tweets.filter(tweet => {
    return tweet.tweetData?.legacy?.inReplyToStatusIdStr != null;
  }).sort((a, b) => {
    const aTime = new Date(a.tweetData?.legacy?.createdAt || 0).getTime();
    const bTime = new Date(b.tweetData?.legacy?.createdAt || 0).getTime();
    return bTime - aTime;
  });
}

// 按互动数排序获取推文
export async function getUserTweetsSortedByEngagement(userId: string, limit?: number): Promise<UserTweets[]> {
  const tweets = await db.userTweets
    .where('authorUserId')
    .equals(userId)
    .toArray();

  // 按总互动数排序（点赞 + 转推 + 回复）
  const sortedTweets = tweets.sort((a, b) => {
    const aLegacy = a.tweetData?.legacy;
    const bLegacy = b.tweetData?.legacy;
    const aEngagement = (aLegacy?.favoriteCount || 0) + (aLegacy?.retweetCount || 0) + (aLegacy?.replyCount || 0);
    const bEngagement = (bLegacy?.favoriteCount || 0) + (bLegacy?.retweetCount || 0) + (bLegacy?.replyCount || 0);
    return bEngagement - aEngagement;
  });

  return limit ? sortedTweets.slice(0, limit) : sortedTweets;
}

// 添加单条推文
export async function addUserTweet(
  tweetId: string,
  authorUserId: string,
  tweetRawData: Tweet,
  authorRawData: User
): Promise<void> {
  const tweet: UserTweets = {
    id: tweetId,
    authorUserId: authorUserId,
    tweetData: tweetRawData,
    authorData: authorRawData,
    exportTime: Date.now()
  };

  await db.userTweets.add(tweet);
}



// ==================== 简单统计函数 ====================

// 获取用户数据统计
export async function getUserDataStats(userId: string) {
  const stats = {
    followersCount: await db.userFollowers
      .where('targetUserId')
      .equals(userId)
      .count(),

    verifiedFollowersCount: (await db.userFollowers
      .where('targetUserId')
      .equals(userId)
      .toArray())
      .filter(f => f.data?.legacy?.verified === true)
      .length,

    followingCount: await db.userFollowing
      .where('sourceUserId')
      .equals(userId)
      .count(),

    verifiedFollowingCount: (await db.userFollowing
      .where('sourceUserId')
      .equals(userId)
      .toArray())
      .filter(f => f.data?.legacy?.verified === true)
      .length,

    tweetsCount: await db.userTweets
      .where('authorUserId')
      .equals(userId)
      .count(),

    originalTweetsCount: (await db.userTweets
      .where('authorUserId')
      .equals(userId)
      .toArray())
      .filter(tweet => {
        const legacy = tweet.tweetData?.legacy;
        return !legacy?.retweetedStatusResult && !legacy?.inReplyToStatusIdStr;
      })
      .length,

    retweetsCount: (await db.userTweets
      .where('authorUserId')
      .equals(userId)
      .toArray())
      .filter(tweet => tweet.tweetData?.legacy?.retweetedStatusResult != null)
      .length,

    repliesCount: (await db.userTweets
      .where('authorUserId')
      .equals(userId)
      .toArray())
      .filter(tweet => tweet.tweetData?.legacy?.inReplyToStatusIdStr != null)
      .length
  };

  return stats;
}

// 数据清理函数
export async function cleanupUserData(userId: string): Promise<void> {
  // 删除用户的所有数据
  await db.userFollowers.where('targetUserId').equals(userId).delete();
  await db.userFollowing.where('sourceUserId').equals(userId).delete();
  await db.userTweets.where('authorUserId').equals(userId).delete();
  await db.users.delete(userId);
}

// 导出用户数据到 JSON
export async function exportUserDataToJSON(userId: string): Promise<any> {
  const user = await getUser(userId);
  if (!user) throw new Error('User not found');

  const result = {
    user,
    exportTime: new Date().toISOString(),
    data: {
      followers: await getUserFollowers(userId),
      following: await getUserFollowing(userId),
      tweets: await getUserTweets(userId)
    },
    stats: await getUserDataStats(userId)
  };

  return result;
}

// 导出用户数据到 CSV
export async function exportUserDataToCSV(userId: string, dataType: 'followers' | 'following' | 'tweets'): Promise<string> {
  let csvContent = '';

  switch (dataType) {
    case 'followers':
      const followers = await getUserFollowers(userId);
      csvContent = 'Username,Name,Verified,Followers Count,Profile Image URL,Export Time\n';
      csvContent += followers.map(follower => {
        const legacy = follower.data?.legacy;
        return `"${legacy?.screenName || ''}","${legacy?.name || ''}",${legacy?.verified || false},${legacy?.followersCount || 0},"${legacy?.profileImageUrlHttps || ''}","${new Date(follower.exportTime).toISOString()}"`;
      }).join('\n');
      break;

    case 'following':
      const following = await getUserFollowing(userId);
      csvContent = 'Username,Name,Verified,Followers Count,Profile Image URL,Export Time\n';
      csvContent += following.map(follow => {
        const legacy = follow.data?.legacy;
        return `"${legacy?.screenName || ''}","${legacy?.name || ''}",${legacy?.verified || false},${legacy?.followersCount || 0},"${legacy?.profileImageUrlHttps || ''}","${new Date(follow.exportTime).toISOString()}"`;
      }).join('\n');
      break;

    case 'tweets':
      const tweets = await getUserTweets(userId);
      csvContent = 'Tweet ID,Text,Created At,Retweets,Likes,Replies,Quotes\n';
      csvContent += tweets.map(tweet => {
        const legacy = tweet.tweetData?.legacy;
        return `"${legacy?.idStr || ''}","${(legacy?.fullText || '').replace(/"/g, '""')}","${legacy?.createdAt || ''}",${legacy?.retweetCount || 0},${legacy?.favoriteCount || 0},${legacy?.replyCount || 0},${legacy?.quoteCount || 0}`;
      }).join('\n');
      break;
  }

  return csvContent;
}
