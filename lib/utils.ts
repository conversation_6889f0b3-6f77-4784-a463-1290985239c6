import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}


export function formatVideoSize(
  bitrate: number,
  durationMillis?: number,
  fractionDigits = 2
): string {
  if (!bitrate || bitrate <= 0) {
    return "? MB"
  }

  if (!durationMillis || durationMillis <= 0) {
    // 如果没有时长信息，只显示比特率
    return `${Math.round(bitrate / 1000)}kbps`
  }

  const durationSec = durationMillis / 1000
  // 注意：这是理论文件大小，实际大小可能因为以下原因有差异：
  // 1. 视频编码效率和压缩比
  // 2. 音频轨道大小（bitrate 通常只包含视频部分）
  // 3. 文件容器格式的开销
  // 4. 可变比特率 vs 固定比特率
  const estimatedSizeBytes = (bitrate * durationSec) / 8

  // 直接转换为 MB，最小单位为 MB
  const sizeInMB = estimatedSizeBytes / (1024 * 1024)
  
  // 如果小于 1MB，显示为 0.x MB
  if (sizeInMB < 1) {
    return `~${sizeInMB.toFixed(fractionDigits)} MB`
  }

  // 处理更大的单位
  const units = ["MB", "GB", "TB"]
  let size = sizeInMB
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  return `~${size.toFixed(fractionDigits)} ${units[unitIndex]}`
}


/**
 * 提取 url 的 filename
 * @param url 
 * @returns 
 */
export function extractFilenameFromUrl(url: string): string {
  try {
    const parsedUrl = new URL(url)
    const pathname = parsedUrl.pathname // e.g. /amplify_video/.../jDKQ7W1SViAkjelD.mp4
    const segments = pathname.split("/")
    return segments.pop() || "" // 最后一段就是文件名
  } catch (e) {
    return "" // 非法 URL，返回空字符串
  }
}
