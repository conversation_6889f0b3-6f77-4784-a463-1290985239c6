import { sendToBackground } from "@plasmohq/messaging";
import { useQuery } from "@tanstack/react-query"
import { TwitterOpenApi, type TwitterOpenApiClient } from "twitter-openapi-typescript"
import type { TwitterApiUtilsResponse, TimelineApiUtilsResponse, UserApiUtilsData } from "twitter-openapi-typescript"

let _client: TwitterOpenApiClient
const getClient = async () => {
  if (_client) return _client
  const res = await sendToBackground({
    name: 'get-cookies'
  })
  const api = new TwitterOpenApi();
  const _client = await api.getClientFromCookies(res.cookieObj);
  return _client
}

export const useSearchUser = (username: string) => {
  return useQuery({
    queryKey: ['user', username],
    queryFn: async () => {
      const client = await getClient()
      if (!client || !username) throw new Error('Client or username not available')

      const res = await client.getUserApi().getUserByScreenName({
        screenName: username
      })

      // 转换数据格式
      const user = res.data.user

      return user
    },
    enabled: !!username,
  })
}

// 导出flowers - 使用限流的useQuery版本
export const userExportFlowers = (userId: string, selectedExportType: string, cursor?: string, allowFetch: boolean = false) => {
  const isAllowedType = selectedExportType === 'flowers' || selectedExportType === 'flowing'

  return useQuery({
    queryKey: ['export', selectedExportType, userId, cursor],
    queryFn: async () => {
      const client = await getClient()
      if (!client || !userId) throw new Error('Client or userId not available')

      let res: TwitterApiUtilsResponse<TimelineApiUtilsResponse<UserApiUtilsData>>
      if (selectedExportType === 'flowers') {
        res = await client.getUserListApi().getFollowers({
          userId,
          count: 100,
          cursor
        })
      } else {
        res = await client.getUserListApi().getFollowing({
          userId,
          count: 100,
          cursor
        })
      }

      return res.data
    },
    enabled: !!userId && isAllowedType && allowFetch,
    // 配置限流：一分钟25次请求，每次间隔2秒
    refetchInterval: 2000, // 每2秒请求一次
    refetchIntervalInBackground: false,
    staleTime: 2000, // 2秒后数据过期
    gcTime: 60000, // 1分钟后清理缓存
    // 限制重试次数避免过于频繁的请求
    retry: 1,
    retryDelay: 2000,
  })
}




// 导出类型定义
export type ExportType =
  | 'followers'           // 关注者
  | 'verifiedFollowers'   // 已验证的关注者
  | 'following'           // 正在关注
  | 'tweets'              // 推文
  | 'tweetsAndReplies'    // 推文和回复
  | 'media'               // 媒体推文
  | 'likes'               // 点赞的推文

// 自适应导出数据的 hook
export const useExportUserData = (userId: string, exportType: ExportType, options?: {
  count?: number
  cursor?: string
  enabled?: boolean
}) => {
  const { count = 100, cursor, enabled = true } = options || {}

  return useQuery({
    queryKey: ['export', exportType, userId, cursor],
    queryFn: async () => {
      const client = await getClient()
      if (!client || !userId) throw new Error('Client or userId not available')

      let res: any

      switch (exportType) {
        case 'followers':
        case 'verifiedFollowers':
          res = await client.getUserListApi().getFollowers({
            userId,
            count,
            cursor
          })
          break

        case 'following':
          res = await client.getUserListApi().getFollowing({
            userId,
            count,
            cursor
          })
          break

        case 'tweets':
          res = await client.getTweetApi().getUserTweets({
            userId,
            count,
            cursor
          })
          break

        case 'tweetsAndReplies':
          res = await client.getTweetApi().getUserTweetsAndReplies({
            userId,
            count,
            cursor
          })
          break

        case 'media':
          res = await client.getTweetApi().getUserMedia({
            userId,
            count,
            cursor
          })
          break

        case 'likes':
          res = await client.getTweetApi().getLikes({
            userId,
            count,
            cursor
          })
          break

        default:
          throw new Error(`Unsupported export type: ${exportType}`)
      }

      return res
    },
    enabled: !!userId && enabled,
  })
}

// 获取用户所有数据的分页 hook（用于批量导出）
export const useExportAllUserData = (userId: string, exportType: ExportType, options?: {
  batchSize?: number
  maxPages?: number
  enabled?: boolean
}) => {
  const { batchSize = 200, maxPages = 50, enabled = true } = options || {}

  return useQuery({
    queryKey: ['export', 'all', exportType, userId, batchSize, maxPages],
    queryFn: async () => {
      const client = await getClient()
      if (!client || !userId) throw new Error('Client or userId not available')

      const allData: any[] = []
      let cursor: string | undefined = undefined
      let pageCount = 0
      let hasMore = true

      while (hasMore && pageCount < maxPages) {
        let res: any

        switch (exportType) {
          case 'followers':
          case 'verifiedFollowers':
            res = await client.getUserListApi().getFollowers({
              userId,
              count: batchSize,
              cursor
            })
            break

          case 'following':
            res = await client.getUserListApi().getFollowing({
              userId,
              count: batchSize,
              cursor
            })
            break

          case 'tweets':
            res = await client.getTweetApi().getUserTweets({
              userId,
              count: batchSize,
              cursor
            })
            break

          case 'tweetsAndReplies':
            res = await client.getTweetApi().getUserTweetsAndReplies({
              userId,
              count: batchSize,
              cursor
            })
            break

          case 'media':
            res = await client.getTweetApi().getUserMedia({
              userId,
              count: batchSize,
              cursor
            })
            break

          case 'likes':
            res = await client.getTweetApi().getLikes({
              userId,
              count: batchSize,
              cursor
            })
            break

          default:
            throw new Error(`Unsupported export type: ${exportType}`)
        }

        console.log('%c [ res.data ]-197', 'font-size:13px; background:pink; color:#bf2c9f;', res.data)
        if (res.data && res.data.data.length > 0) {
          // 根据导出类型过滤已验证用户（仅对 verifiedFollowers 适用）
          let filteredData = res.data.data
          if (exportType === 'verifiedFollowers') {
            filteredData = res.data.data.filter((item: any) =>
              item.user?.legacy?.verified === true || item.user?.isBlueVerified === true
            )
          }

          allData.push(...filteredData)
        }

        // 检查是否还有更多数据
        cursor = res.data?.cursor?.bottom
        hasMore = !!cursor && res.data.data.length > 0
        pageCount++

        // 添加延迟避免请求过于频繁
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      return {
        data: allData,
        totalCount: allData.length,
        pageCount,
        hasMore,
        lastCursor: cursor
      }
    },
    enabled: !!userId && enabled,
    // 增加缓存时间，避免重复请求
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 10 * 60 * 1000, // 10分钟
  })
}