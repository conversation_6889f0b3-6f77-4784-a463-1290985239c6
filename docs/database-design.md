# Twitter 导出功能数据库设计

## 📊 **数据库概览**

基于 Twitter OpenAPI 的数据结构，设计了一个完整的 Twitter 用户数据导出系统的数据库架构。

**数据库名称**: `TgxDB` (统一数据库，整合了所有功能)
**版本管理**: 使用 Dexie 版本控制，向后兼容现有数据

## 🗃️ **表结构设计**

### **1. 用户表 (users)**
存储 Twitter 用户的基础信息

| 字段 | 类型 | 说明 | 索引 |
|------|------|------|------|
| id | string | Twitter 用户 ID | 主键 |
| username | string | @用户名 | 索引 |
| name | string | 显示名称 | 索引 |
| description | string? | 个人简介 | - |
| location | string? | 位置 | - |
| url | string? | 个人网站 | - |
| profile_image_url | string? | 头像 URL | - |
| profile_banner_url | string? | 横幅 URL | - |
| verified | boolean | 是否认证 | 索引 |
| verified_type | string? | 认证类型 | - |
| followers_count | number | 粉丝数 | 索引 |
| following_count | number | 关注数 | 索引 |
| tweet_count | number | 推文数 | - |
| listed_count | number | 被列表数 | - |
| created_at | string | 账号创建时间 | - |
| protected | boolean | 是否私密账号 | - |
| last_updated | number | 最后更新时间戳 | 索引 |
| export_source | string | 导出来源 | 索引 |

### **2. 关注关系表 (relationships)**
存储 followers 和 following 关系

| 字段 | 类型 | 说明 | 索引 |
|------|------|------|------|
| id | number | 自增主键 | 主键 |
| source_user_id | string | 源用户ID | 索引 |
| target_user_id | string | 目标用户ID | 索引 |
| relationship_type | string | 关系类型 (follower/following) | 索引 |
| created_at | string? | 关注时间 | - |
| source_username | string | 源用户名 | - |
| target_username | string | 目标用户名 | - |
| target_name | string | 目标显示名 | - |
| target_profile_image_url | string? | 目标头像 URL | - |
| target_verified | boolean | 目标是否认证 | - |
| target_followers_count | number | 目标粉丝数 | - |
| export_time | number | 导出时间戳 | 索引 |
| export_batch_id | string | 导出批次ID | 索引 |

**复合索引**: `[source_user_id+relationship_type]` - 快速查询特定用户的关注关系

### **3. 推文表 (tweets)**
存储用户的推文数据

| 字段 | 类型 | 说明 | 索引 |
|------|------|------|------|
| id | string | 推文 ID | 主键 |
| text | string | 推文内容 | - |
| author_id | string | 作者 ID | 索引 |
| author_username | string | 作者用户名 | 索引 |
| author_name | string | 作者显示名 | - |
| created_at | string | 发布时间 | 索引 |
| public_metrics | object | 互动数据 (转发、点赞等) | - |
| tweet_type | string | 推文类型 | 索引 |
| conversation_id | string? | 对话 ID | 索引 |
| in_reply_to_user_id | string? | 回复的用户 ID | - |
| referenced_tweets | array? | 引用的推文 | - |
| attachments | object? | 媒体附件 | - |
| entities | object? | 实体信息 (链接、标签等) | - |
| geo | object? | 地理位置 | - |
| export_time | number | 导出时间戳 | 索引 |
| export_batch_id | string | 导出批次ID | 索引 |
| export_source | string | 导出来源用户 | 索引 |

### **4. 媒体表 (media)**
存储推文中的媒体文件信息

| 字段 | 类型 | 说明 | 索引 |
|------|------|------|------|
| media_key | string | 媒体 key | 主键 |
| type | string | 媒体类型 (photo/video/gif) | 索引 |
| url | string? | 媒体 URL | - |
| preview_image_url | string? | 预览图 URL | - |
| width | number? | 宽度 | - |
| height | number? | 高度 | - |
| duration_ms | number? | 视频时长 | - |
| tweet_id | string | 所属推文 ID | 索引 |
| local_path | string? | 本地存储路径 | - |
| download_status | string | 下载状态 | 索引 |
| download_time | number? | 下载时间戳 | - |
| file_size | number? | 文件大小 | - |
| export_time | number | 导出时间戳 | 索引 |
| export_batch_id | string | 导出批次ID | 索引 |

### **5. 导出任务表 (exportTasks)**
管理导出任务的生命周期

| 字段 | 类型 | 说明 | 索引 |
|------|------|------|------|
| id | string | 任务 ID | 主键 |
| target_username | string | 目标用户名 | 索引 |
| target_user_id | string | 目标用户 ID | 索引 |
| export_types | array | 导出类型数组 | - |
| status | string | 任务状态 | 索引 |
| progress | number | 进度百分比 | - |
| total_items | number? | 总项目数 | - |
| completed_items | number | 已完成项目数 | - |
| failed_items | number | 失败项目数 | - |
| created_at | number | 创建时间 | 索引 |
| started_at | number? | 开始时间 | - |
| completed_at | number? | 完成时间 | 索引 |
| error_message | string? | 错误信息 | - |
| error_details | any? | 错误详情 | - |
| config | object | 配置信息 | - |

### **6. 导出批次表 (exportBatches)**
管理分页导出的批次信息

| 字段 | 类型 | 说明 | 索引 |
|------|------|------|------|
| id | string | 批次 ID | 主键 |
| task_id | string | 关联的任务 ID | 索引 |
| batch_type | string | 批次类型 | 索引 |
| page_token | string? | 分页令牌 | - |
| page_number | number | 页码 | - |
| items_count | number | 本批次项目数 | - |
| status | string | 批次状态 | 索引 |
| created_at | number | 创建时间 | 索引 |
| processed_at | number? | 处理时间 | 索引 |
| rate_limit_remaining | number? | API 限制剩余 | - |
| rate_limit_reset | number? | API 限制重置时间 | - |

## 🔍 **查询优化**

### **高频查询场景**
1. **获取用户的关注者**: `relationships` 表按 `[source_user_id+relationship_type]` 查询
2. **获取用户的推文**: `tweets` 表按 `author_id` 查询
3. **获取任务进度**: `exportTasks` 和 `exportBatches` 联合查询
4. **媒体下载管理**: `media` 表按 `download_status` 查询

### **索引策略**
- **复合索引**: 用于多字段组合查询
- **单字段索引**: 用于常见的过滤和排序
- **时间索引**: 支持按时间范围查询和数据清理

## 📈 **扩展性考虑**

### **数据分片**
- 按用户 ID 分片存储大量数据
- 按时间分片进行历史数据归档

### **性能优化**
- 冗余字段减少 JOIN 查询
- 批量操作提高写入性能
- 定期清理过期数据

### **备份策略**
- 定期导出重要数据到 JSON/CSV
- 支持增量备份和恢复

## 🛠️ **API 设计**

提供了完整的数据库操作 API：
- 用户管理: `saveUser`, `getUser`, `getUserByUsername`
- 关系管理: `saveRelationships`, `getFollowers`, `getFollowing`
- 推文管理: `saveTweets`, `getUserTweets`, `getTweetsByBatch`
- 媒体管理: `saveMedia`, `getTweetMedia`, `getPendingDownloads`
- 任务管理: `createExportTask`, `updateTaskStatus`, `getExportTask`
- 统计分析: `getExportStats`, `exportToJSON`, `exportToCSV`
- 数据清理: `cleanupOldData`

这个设计充分考虑了 Twitter OpenAPI 的数据结构，支持完整的导出功能，并具有良好的扩展性和性能。
