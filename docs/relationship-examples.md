# Twitter 关注关系多对多设计示例

## 🔗 **关系模型说明**

新的 `TwitterRelationship` 表完全支持多对多关系，能够处理所有复杂的关注场景。

## 📊 **数据结构**

```typescript
interface TwitterRelationship {
  id?: number;
  user_a_id: string;        // 关注者ID
  user_b_id: string;        // 被关注者ID
  relationship_type: 'following' | 'mutual';  // 关系类型
  // ... 其他字段
}
```

## 🎯 **关系类型说明**

### **1. 单向关注 (`following`)**
- `user_a` 关注 `user_b`
- `user_b` 不关注 `user_a`

### **2. 相互关注 (`mutual`)**
- `user_a` 和 `user_b` 互相关注
- 只需要一条记录表示双向关系

## 📝 **实际使用示例**

### **场景1: 用户A关注多个用户**

```sql
-- A 关注 B、C、D
{ user_a_id: "A", user_b_id: "B", relationship_type: "following" }
{ user_a_id: "A", user_b_id: "C", relationship_type: "following" }
{ user_a_id: "A", user_b_id: "D", relationship_type: "following" }
```

### **场景2: 多个用户关注A**

```sql
-- B、C、D 关注 A
{ user_a_id: "B", user_b_id: "A", relationship_type: "following" }
{ user_a_id: "C", user_b_id: "A", relationship_type: "following" }
{ user_a_id: "D", user_b_id: "A", relationship_type: "following" }
```

### **场景3: 相互关注**

```sql
-- A 和 B 相互关注（只需一条记录）
{ user_a_id: "A", user_b_id: "B", relationship_type: "mutual" }
```

### **场景4: 复杂关系网络**

```sql
-- 用户关系网络示例
{ user_a_id: "Alice", user_b_id: "Bob", relationship_type: "mutual" }     -- 相互关注
{ user_a_id: "Alice", user_b_id: "Charlie", relationship_type: "following" } -- Alice关注Charlie
{ user_a_id: "David", user_b_id: "Alice", relationship_type: "following" }   -- David关注Alice
{ user_a_id: "Bob", user_b_id: "Charlie", relationship_type: "following" }   -- Bob关注Charlie
```

## 🔍 **查询示例**

### **1. 获取用户的关注者**

```typescript
// 获取Alice的关注者
const followers = await getFollowers("Alice");
// 返回所有 user_b_id = "Alice" 的记录

// 结果包括：
// - David (单向关注Alice)
// - Bob (与Alice相互关注)
```

### **2. 获取用户关注的人**

```typescript
// 获取Alice关注的人
const following = await getFollowing("Alice");
// 返回所有 user_a_id = "Alice" 的记录

// 结果包括：
// - Bob (相互关注)
// - Charlie (Alice单向关注)
```

### **3. 获取相互关注**

```typescript
// 获取Alice的相互关注
const mutualFollows = await getMutualFollows("Alice");
// 返回所有 relationship_type = "mutual" 且包含Alice的记录

// 结果包括：
// - Bob (相互关注)
```

### **4. 检查两用户关系**

```typescript
// 检查Alice和Bob的关系
const relationship = await getRelationshipBetweenUsers("Alice", "Bob");

// 可能的结果：
// { aFollowsB: true, bFollowsA: true, mutual: true }   // 相互关注
// { aFollowsB: true, bFollowsA: false, mutual: false } // Alice关注Bob
// { aFollowsB: false, bFollowsA: true, mutual: false } // Bob关注Alice
// { aFollowsB: false, bFollowsA: false, mutual: false } // 无关系
```

## 📈 **性能优化**

### **索引设计**
```typescript
// 复合索引支持高效查询
'[user_a_id+user_b_id]',      // 快速查找特定用户对关系
'[user_a_id+relationship_type]', // 快速查找用户的关注列表
'[user_b_id+relationship_type]', // 快速查找用户的关注者列表
```

### **查询优化**
- **单向查询**: O(log n) 时间复杂度
- **双向查询**: 两次 O(log n) 查询
- **批量查询**: 使用 `bulkPut` 提高写入性能

## 🔄 **关系状态转换**

### **从单向到相互关注**

```typescript
// 初始状态：A关注B
{ user_a_id: "A", user_b_id: "B", relationship_type: "following" }

// B也关注A时，自动转换为相互关注
await addOrUpdateRelationship("B", "A", ...);

// 结果：删除原记录，创建相互关注记录
{ user_a_id: "A", user_b_id: "B", relationship_type: "mutual" }
```

### **从相互关注到单向**

```typescript
// 如果其中一方取消关注，需要：
// 1. 删除 mutual 记录
// 2. 创建新的 following 记录
```

## 📊 **统计查询**

```typescript
// 获取用户的完整关注统计
const stats = {
  followers: await getFollowers(userId).length,      // 关注者数量
  following: await getFollowing(userId).length,      // 关注数量
  mutualFollows: await getMutualFollows(userId).length, // 相互关注数量
};

// 获取认证用户关注者
const verifiedFollowers = followers.filter(rel => 
  rel.user_a_verified || rel.user_b_verified
);
```

## ✅ **优势总结**

1. **完整支持多对多**: 一个用户可以关注多个用户，也可以被多个用户关注
2. **相互关系优化**: 相互关注只需一条记录，节省存储空间
3. **高效查询**: 复合索引支持各种查询场景
4. **关系状态管理**: 自动处理单向到双向的转换
5. **数据完整性**: 冗余字段避免复杂的 JOIN 查询
6. **扩展性**: 支持大规模用户关系网络

这个设计完全满足 Twitter 复杂的关注关系需求！
