# 简化的 Twitter 用户数据库设计

## 🎯 **设计目标**

每个用户的 followers、following、verified followers 和 tweets 数据都**单独导出和存储**，不进行用户关系合并。

## 📊 **核心表结构 (4张表)**

### **1. 用户基础信息表 (users)**
```typescript
interface TwitterUser {
  id: string;                    // Twitter 用户 ID (主键)
  username: string;              // @用户名
  name: string;                  // 显示名称
  description?: string;          // 个人简介
  location?: string;             // 位置
  url?: string;                  // 个人网站
  profile_image_url?: string;    // 头像 URL
  profile_banner_url?: string;   // 横幅 URL
  verified: boolean;             // 是否认证
  verified_type?: 'blue' | 'business' | 'government';
  followers_count: number;       // 粉丝数
  following_count: number;       // 关注数
  tweet_count: number;           // 推文数
  listed_count: number;          // 被列表数
  created_at: string;            // 账号创建时间
  protected: boolean;            // 是否私密账号
  last_updated: number;          // 最后更新时间戳
  export_source: string;         // 导出来源
}
```

### **2. 用户关注者表 (userFollowers)**
```typescript
interface UserFollowers {
  id?: number;                   // 自增主键
  target_user_id: string;        // 被关注的用户ID（数据所属用户）
  target_username: string;       // 被关注的用户名
  
  // 关注者详细信息
  follower_id: string;           // 关注者ID
  follower_username: string;     // 关注者用户名
  follower_name: string;         // 关注者显示名
  follower_profile_image_url?: string;
  follower_verified: boolean;    // 是否认证
  follower_verified_type?: 'blue' | 'business' | 'government';
  follower_followers_count: number;
  follower_following_count: number;
  follower_description?: string;
  follower_location?: string;
  
  followed_at?: string;          // 关注时间
  export_time: number;           // 导出时间戳
}
```

### **3. 用户关注列表表 (userFollowing)**
```typescript
interface UserFollowing {
  id?: number;                   // 自增主键
  source_user_id: string;        // 关注者用户ID（数据所属用户）
  source_username: string;       // 关注者用户名
  
  // 被关注者详细信息
  following_id: string;          // 被关注者ID
  following_username: string;    // 被关注者用户名
  following_name: string;        // 被关注者显示名
  following_profile_image_url?: string;
  following_verified: boolean;   // 是否认证
  following_verified_type?: 'blue' | 'business' | 'government';
  following_followers_count: number;
  following_following_count: number;
  following_description?: string;
  following_location?: string;
  
  followed_at?: string;          // 关注时间
  export_time: number;           // 导出时间戳
}
```

### **4. 用户推文表 (userTweets)**
```typescript
interface UserTweets {
  id: string;                    // 推文 ID (主键)
  author_id: string;             // 作者 ID（数据所属用户）
  author_username: string;       // 作者用户名
  author_name: string;           // 作者显示名
  
  // 推文内容
  text: string;                  // 推文内容
  created_at: string;            // 发布时间
  
  // 互动数据（简化）
  retweet_count: number;
  like_count: number;
  reply_count: number;
  quote_count: number;
  bookmark_count?: number;
  impression_count?: number;
  
  // 推文类型
  tweet_type: 'original' | 'retweet' | 'quote' | 'reply';
  conversation_id?: string;
  in_reply_to_user_id?: string;
  
  // 引用推文信息
  referenced_tweet_id?: string;
  referenced_tweet_type?: 'retweeted' | 'quoted' | 'replied_to';
  
  // 媒体和链接（简化）
  has_media: boolean;
  media_count?: number;
  media_types?: string[];
  has_urls: boolean;
  urls_count?: number;
  hashtags_count?: number;
  mentions_count?: number;
  
  // 地理位置
  has_geo: boolean;
  place_name?: string;
  
  export_time: number;           // 导出时间戳
}
```

## 🔧 **核心操作函数**

### **用户管理**
```typescript
saveUser(user: TwitterUser)
getUser(userId: string)
getUserByUsername(username: string)
```

### **关注者管理**
```typescript
// 基础操作
saveUserFollowers(followers: UserFollowers[])
getUserFollowers(userId: string)
getUserVerifiedFollowers(userId: string)
getUserFollowersSortedByFollowersCount(userId: string, limit?: number)

// 添加单个关注者
addUserFollower(targetUserId, targetUsername, followerData, followedAt?)
```

### **关注列表管理**
```typescript
// 基础操作
saveUserFollowing(following: UserFollowing[])
getUserFollowing(userId: string)
getUserVerifiedFollowing(userId: string)
getUserFollowingSortedByFollowersCount(userId: string, limit?: number)

// 添加单个关注
addUserFollowing(sourceUserId, sourceUsername, followingData, followedAt?)
```

### **推文管理**
```typescript
// 基础操作
saveUserTweets(tweets: UserTweets[])
getUserTweets(userId: string)
getUserOriginalTweets(userId: string)
getUserRetweets(userId: string)
getUserReplies(userId: string)
getUserTweetsSortedByEngagement(userId: string, limit?: number)

// 添加单条推文
addUserTweet(authorId, authorUsername, authorName, tweetData)
```

### **统计和导出**
```typescript
// 统计
getUserDataStats(userId: string)

// 导出
exportUserDataToJSON(userId: string)
exportUserDataToCSV(userId: string, dataType: 'followers' | 'following' | 'tweets')

// 清理
cleanupUserData(userId: string)
```

## 🎯 **使用示例**

### **导出用户 @elonmusk 的完整数据**
```typescript
// 1. 获取用户基础信息
const user = await getUser("elon_user_id");

// 2. 获取关注者
const followers = await getUserFollowers("elon_user_id");
const verifiedFollowers = await getUserVerifiedFollowers("elon_user_id");

// 3. 获取关注列表
const following = await getUserFollowing("elon_user_id");
const verifiedFollowing = await getUserVerifiedFollowing("elon_user_id");

// 4. 获取推文
const tweets = await getUserTweets("elon_user_id");
const originalTweets = await getUserOriginalTweets("elon_user_id");

// 5. 获取统计信息
const stats = await getUserDataStats("elon_user_id");

// 6. 导出为 JSON
const jsonData = await exportUserDataToJSON("elon_user_id");

// 7. 导出为 CSV
const followersCSV = await exportUserDataToCSV("elon_user_id", "followers");
const tweetsCSV = await exportUserDataToCSV("elon_user_id", "tweets");
```

### **批量添加数据**
```typescript
// 批量添加关注者
const followersData = [/* API 返回的关注者数据 */];
for (const followerData of followersData) {
  await addUserFollower("elon_user_id", "elonmusk", followerData);
}

// 批量添加推文
const tweetsData = [/* API 返回的推文数据 */];
for (const tweetData of tweetsData) {
  await addUserTweet("elon_user_id", "elonmusk", "Elon Musk", tweetData);
}
```

## ✅ **设计优势**

1. **数据独立性**：每个用户的数据完全独立，不会相互影响
2. **查询简单**：直接查询特定用户的数据，无需复杂的关系判断
3. **导出灵活**：可以单独导出任何用户的任何类型数据
4. **结构清晰**：数据结构简单明了，易于理解和维护
5. **扩展性好**：添加新字段或新用户不影响现有数据
6. **无关系复杂性**：避免了多对多关系的复杂逻辑

## 🚀 **总结**

这个简化设计完美符合你的需求：
- ✅ **每个用户数据独立导出**
- ✅ **不合并用户关系**
- ✅ **支持 followers、following、verified followers、tweets 单独导出**
- ✅ **查询简单高效**
- ✅ **数据结构清晰**
- ✅ **移除了无关的任务管理、媒体下载等复杂功能**

现在你可以专注于核心的用户数据导出功能！
