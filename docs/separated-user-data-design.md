# 分离式用户数据导出设计

## 🎯 **设计理念**

每个用户的 followers、following、verified followers 和 tweets 数据都**单独导出和存储**，不进行用户关系合并。

## 📊 **新的表结构**

### **1. 用户关注者表 (userFollowers)**
存储每个用户的关注者列表

```typescript
interface UserFollowers {
  id?: number;                    // 自增主键
  target_user_id: string;         // 被关注的用户ID（数据所属用户）
  target_username: string;        // 被关注的用户名
  
  // 关注者详细信息
  follower_id: string;            // 关注者ID
  follower_username: string;      // 关注者用户名
  follower_name: string;          // 关注者显示名
  follower_profile_image_url?: string;
  follower_verified: boolean;     // 是否认证
  follower_verified_type?: 'blue' | 'business' | 'government';
  follower_followers_count: number;
  follower_following_count: number;
  follower_description?: string;
  follower_location?: string;
  
  followed_at?: string;           // 关注时间
  export_time: number;            // 导出时间戳
  export_batch_id: string;        // 导出批次ID
}
```

### **2. 用户关注列表表 (userFollowing)**
存储每个用户关注的人

```typescript
interface UserFollowing {
  id?: number;                    // 自增主键
  source_user_id: string;         // 关注者用户ID（数据所属用户）
  source_username: string;        // 关注者用户名
  
  // 被关注者详细信息
  following_id: string;           // 被关注者ID
  following_username: string;     // 被关注者用户名
  following_name: string;         // 被关注者显示名
  following_profile_image_url?: string;
  following_verified: boolean;    // 是否认证
  following_verified_type?: 'blue' | 'business' | 'government';
  following_followers_count: number;
  following_following_count: number;
  following_description?: string;
  following_location?: string;
  
  followed_at?: string;           // 关注时间
  export_time: number;            // 导出时间戳
  export_batch_id: string;        // 导出批次ID
}
```

## 🔍 **查询功能**

### **基础查询**
```typescript
// 获取用户A的所有关注者
const followers = await getUserFollowers("userA_id");

// 获取用户A关注的所有人
const following = await getUserFollowing("userA_id");

// 获取用户A的认证关注者
const verifiedFollowers = await getUserVerifiedFollowers("userA_id");

// 获取用户A关注的认证用户
const verifiedFollowing = await getUserVerifiedFollowing("userA_id");
```

### **高级查询**
```typescript
// 按关注者数量排序获取关注者（找出影响力大的关注者）
const topFollowers = await getUserFollowersSortedByFollowersCount("userA_id", 100);

// 按关注者数量排序获取关注列表（找出影响力大的被关注者）
const topFollowing = await getUserFollowingSortedByFollowersCount("userA_id", 100);
```

## 📈 **数据导出场景**

### **场景1：导出用户A的关注者**
```typescript
// 1. 普通关注者
const allFollowers = await getUserFollowers("userA_id");

// 2. 只要认证关注者
const verifiedFollowers = await getUserVerifiedFollowers("userA_id");

// 3. 按影响力排序的关注者
const influentialFollowers = await getUserFollowersSortedByFollowersCount("userA_id");
```

### **场景2：导出用户A关注的人**
```typescript
// 1. 所有关注的人
const allFollowing = await getUserFollowing("userA_id");

// 2. 只要关注的认证用户
const verifiedFollowing = await getUserVerifiedFollowing("userA_id");

// 3. 按影响力排序的关注列表
const influentialFollowing = await getUserFollowingSortedByFollowersCount("userA_id");
```

### **场景3：导出用户A的推文**
```typescript
// 用户的所有推文（使用现有的 tweets 表）
const userTweets = await getUserTweets("userA_id");
```

## 🎯 **优势对比**

### **✅ 分离式设计的优势：**

1. **数据独立性**：每个用户的数据完全独立，不会相互影响
2. **查询简单**：直接查询特定用户的数据，无需复杂的关系判断
3. **导出灵活**：可以单独导出任何用户的任何类型数据
4. **存储清晰**：数据结构清晰，易于理解和维护
5. **扩展性好**：添加新字段或新用户不影响现有数据

### **❌ 关系合并设计的问题：**

1. **数据复杂**：需要处理双向关系、相互关注等复杂逻辑
2. **查询复杂**：需要复杂的 JOIN 或多表查询
3. **数据冗余**：同一个关系可能需要存储多次
4. **维护困难**：关系变化时需要更新多个记录

## 📊 **实际使用示例**

### **导出用户 @elonmusk 的数据：**

```typescript
// 1. 导出关注者
const elonFollowers = await getUserFollowers("elon_user_id");
console.log(`Elon 有 ${elonFollowers.length} 个关注者`);

// 2. 导出认证关注者
const elonVerifiedFollowers = await getUserVerifiedFollowers("elon_user_id");
console.log(`其中 ${elonVerifiedFollowers.length} 个是认证用户`);

// 3. 导出关注列表
const elonFollowing = await getUserFollowing("elon_user_id");
console.log(`Elon 关注了 ${elonFollowing.length} 个用户`);

// 4. 导出推文
const elonTweets = await getUserTweets("elon_user_id");
console.log(`Elon 有 ${elonTweets.length} 条推文`);
```

### **导出用户 @tim_cook 的数据：**

```typescript
// 完全独立的数据，不会与 Elon 的数据混淆
const timFollowers = await getUserFollowers("tim_user_id");
const timFollowing = await getUserFollowing("tim_user_id");
const timTweets = await getUserTweets("tim_user_id");
```

## 🔧 **批量操作**

### **批量添加关注者：**
```typescript
// 为用户A批量添加关注者
for (const followerData of followersFromAPI) {
  await addUserFollower(
    "userA_id",
    "userA_username", 
    followerData,
    "batch_123"
  );
}
```

### **批量添加关注列表：**
```typescript
// 为用户A批量添加关注的人
for (const followingData of followingFromAPI) {
  await addUserFollowing(
    "userA_id",
    "userA_username",
    followingData, 
    "batch_456"
  );
}
```

## 📈 **统计分析**

```typescript
// 获取导出任务的统计信息
const stats = await getExportStats("task_123");

// 结果包含：
// - followersCount: 关注者总数
// - verifiedFollowersCount: 认证关注者数
// - followingCount: 关注总数  
// - verifiedFollowingCount: 关注的认证用户数
// - tweetsCount: 推文总数
// - mediaCount: 媒体文件总数
```

## 🎉 **总结**

这种分离式设计完美符合你的需求：
- ✅ **每个用户数据独立**
- ✅ **不合并用户关系**
- ✅ **支持 followers、following、verified followers、tweets 单独导出**
- ✅ **查询简单高效**
- ✅ **数据结构清晰**
- ✅ **易于维护和扩展**

每个用户的数据都是完全独立的，可以单独导出任何用户的任何类型数据，非常适合你的使用场景！
