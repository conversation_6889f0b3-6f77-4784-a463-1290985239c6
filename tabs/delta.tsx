import { usePort } from "@plasmohq/messaging/hook"

type RequestBody = {
  hello: string
}

type ResponseBody = {
  message: string
}

function DeltaTab() {
  const mailPort = usePort<RequestBody, ResponseBody>("mail")

  return (
    <div>
      {mailPort.data?.message}
      <button
        onClick={async () => {
          const res = await mailPort.send({
            hello: "world"
          })
          console.log('%c [ res ]-20', 'font-size:13px; background:pink; color:#bf2c9f;', res)
        }}>
        Send Port
      </button>
    </div>
  )
}

export default DeltaTab
