import { useState, useEffect, useMemo } from "react"
import {
  Button,
  Input,
  Card,
  CardBody,
  CardHeader,
  Chip,
  Avatar,
  RadioGroup,
} from "@heroui/react"
import { Icon } from "@iconify/react"
import { Providers } from "~lib/providers"
import { userExportFollows, useSearchUser } from "~hooks/useTwitter"
import FollowRadio from "~components/follow-radio"
import "../style.css"

// 导出状态管理
type ExportState = 'idle' | 'exporting' | 'paused' | 'completed'

function ExportContent() {
  const [searchUserName, setSearchUserName] = useState('')
  const [screenName, setScreenName] = useState('')
  const [activeTab, setActiveTab] = useState('search')

  const [selectedExportType, setSelectedExportType] = useState<string>('')
  const [exportState, setExportState] = useState<ExportState>('idle')

  const { data: userData, isLoading: userLoading } = useSearchUser(screenName)

  const [cursor, setCursor] = useState<string>()

  const { data: followData, isLoading, refetch } = userExportFollows(
    userData?.restId,
    selectedExportType,
    cursor,
    exportState === 'exporting'
  )

  const followersData = useMemo(() => {
    if (selectedExportType === 'followers') {
      return followData.data.map(item => item.user)
    }
    return []

  }, [followData, selectedExportType])

  // 获取导出类型的显示名称
  const getExportTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      followers: '关注者',
      verifiedFollowers: '已验证的关注者',
      following: '正在关注',
      tweets: '推文',
      tweetsAndReplies: '推文和回复',
      media: '媒体推文',
      likes: '点赞的推文'
    }
    return labels[type] || type
  }

  // 从 URL 参数中获取初始数据
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const username = urlParams.get('username')

    if (username) {
      setSearchUserName(username)
      setScreenName(username)
    }
  }, [])

  // 处理数据获取完成后的游标更新 - 通过定时器实现限流
  useEffect(() => {
    if (followData && exportState === 'exporting') {
      console.log('%c [ Query Data ]-36', 'font-size:13px; background:pink; color:#bf2c9f;', {
        followData,
        isLoading,
        exportState,
        selectedExportType,
        cursor
      })
      // 检查是否已完成（没有数据或数据为空）
      if (!followData.data || followData.data.length === 0) {
        console.log('导出完成：没有更多数据')
        setExportState('completed')
        return
      }

      const nextCursor = followData.cursor.bottom.value
      if (nextCursor !== cursor) {
        console.log('检测到新的 cursor，将在 2 秒后请求下一页')

        // 设置定时器，2秒后更新 cursor（这会触发新的查询）
        const timer = setTimeout(() => {
          console.log('开始请求下一页数据')
          setCursor(nextCursor)
        }, 3000) // 3秒间隔限流

        return () => clearTimeout(timer)
      }
    }
  }, [followData, cursor, exportState])



  // 搜索用户的函数
  const handleSearch = async () => {
    setScreenName(searchUserName.trim())
  }

  // 导出数据的函数
  const handleExport = async () => {
    if (!userData || !selectedExportType) return

    if (exportState === 'idle' || exportState === 'paused') {
      // 开始或继续导出
      if (exportState === 'idle') {
        // 重置游标并开始导出
        setCursor('')
      }
      setExportState('exporting')
      refetch()
    } else if (exportState === 'exporting') {
      // 暂停导出
      setExportState('paused')
    } else if (exportState === 'completed') {
      // 重新开始导出
      setCursor('')
      setExportState('exporting')
      refetch()
    }
  }

  // 获取按钮文本和状态
  const getButtonProps = () => {
    switch (exportState) {
      case 'idle':
        return {
          text: selectedExportType ? `导出${getExportTypeLabel(selectedExportType)}` : '请选择导出类型',
          loading: false,
          disabled: !selectedExportType
        }
      case 'exporting':
        return {
          text: '暂停导出',
          loading: isLoading,
          disabled: false
        }
      case 'paused':
        return {
          text: '继续导出',
          loading: false,
          disabled: false
        }
      case 'completed':
        return {
          text: '已完成',
          loading: false,
          disabled: false
        }
    }
  }

  const buttonProps = getButtonProps()

  return (
    <div className="h-full p-6">
      <div className="max-w-5xl mx-auto my-10">
        {/* 标题部分 */}
        <div className="text-center my-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Icon icon="logos:twitter" className="w-8 h-8" />
            <h1 className="text-3xl font-bold text-gray-800">Twitter 用户数据导出</h1>
          </div>
          <p className="text-gray-600">搜索用户并导出其关注者、正在关注和推文数据</p>
        </div>

        {/* 搜索框 */}
        <Card className="mb-6">
          <CardBody>
            <div className="flex gap-3">
              <Input
                value={searchUserName}
                onChange={(e) => setSearchUserName(e.target.value)}
                placeholder="UserName..."
                className="flex-1"
                size="lg"
                startContent={<Icon icon="gravity-ui:at" className="w-5 h-5 text-gray-400" />}
                onKeyDown={(e: React.KeyboardEvent) => e.key === 'Enter' && handleSearch()}
              />

              <Button
                color="primary"
                size="lg"
                onPress={handleSearch}
                isLoading={userLoading}
                isDisabled={!searchUserName.trim()}
              >
                搜索
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* 用户信息卡片 */}
        {userData && (
          <Card className="mb-6">
            {/* <CardHeader>
              <div className="flex items-center gap-2">
                <Icon icon="heroicons:user" className="w-5 h-5 text-gray-500" />
                <span className="font-semibold text-gray-700">用户资料</span>
              </div>
            </CardHeader> */}
            <CardBody>
              <div className="flex items-start gap-2">
                <Avatar
                  src={userData.legacy.profileImageUrlHttps}
                  name={userData.legacy.name}
                  size="lg"
                  className="flex-shrink-0"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-xl font-bold">{userData.legacy.name}</h3>
                    {userData.isBlueVerified && (
                      <Chip size="sm" color="primary" variant="flat">
                        蓝色验证
                      </Chip>
                    )}
                  </div>
                  <p className="text-gray-600 mb-2">@{userData.legacy.screenName}</p>
                  <p className="text-gray-700 mb-2">{userData.legacy.description}</p>

                  <div className="flex flex-col items-start gap-2 text-sm text-gray-500 mb-2">
                    {userData.legacy?.location && (
                      <div className="flex items-center gap-1">
                        <Icon icon="heroicons:map-pin" className="w-4 h-4" />
                        <span>{userData.legacy.location}</span>
                      </div>
                    )}
                    {userData.legacy.createdAt && (
                      <div className="flex items-center gap-1">
                        <Icon icon="heroicons:calendar" className="w-4 h-4" />
                        <span>加入于 {userData.legacy.createdAt}</span>
                      </div>
                    )}
                  </div>

                  {/* 统计数据 */}
                  <div className="flex gap-4">
                    <div className="flex items-center gap-1">
                      <div className="text-xl font-bold">
                        {userData.legacy.friendsCount.toLocaleString()}
                      </div>
                      <div className="text-sm">正在关注</div>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="text-xl font-bold">
                        {userData.legacy.followersCount.toLocaleString()}
                      </div>
                      <div className="text-sm">关注者</div>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="text-xl font-bold">
                        {userData.legacy.statusesCount.toLocaleString()}
                      </div>
                      <div className="text-sm">推文</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {/* 导出选项 */}
        {userData && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">选择要导出的数据：</h3>
            </CardHeader>
            <CardBody className="flex flex-col gap-3">
              <RadioGroup
                value={selectedExportType}
                onValueChange={setSelectedExportType}
                classNames={{ wrapper: "grid grid-cols-3" }}
              >
                {/* <FollowRadio
                  label={userData.legacy.followersCount.toLocaleString()}
                  value="verifiedFlowers"
                  description='已验证的关注者'
                  disabled={userData.legacy.followersCount === 0}
                /> */}
                <FollowRadio
                  label={userData.legacy.friendsCount.toLocaleString()}
                  value="flowing"
                  description='正在关注'
                  disabled={userData.legacy.friendsCount === 0}
                />
                <FollowRadio
                  label={userData.legacy.followersCount.toLocaleString()}
                  value="flowers"
                  description='关注者'
                  disabled={userData.legacy.followersCount === 0}
                />
                <FollowRadio
                  label={userData.legacy.statusesCount.toLocaleString()}
                  value="tweets"
                  description='推文'
                  disabled={userData.legacy.statusesCount === 0}
                />
              </RadioGroup>

              <Button
                color="primary"
                size="md"
                className="w-full"
                onPress={handleExport}
                // isLoading={buttonProps.loading}
                isDisabled={buttonProps.disabled}
              >
                {buttonProps.text}
              </Button>
            </CardBody>
          </Card>
        )}
      </div>
    </div>
  )
}

function ExportPage() {
  return (
    <Providers>
      <ExportContent />
    </Providers>
  )
}

export default ExportPage
