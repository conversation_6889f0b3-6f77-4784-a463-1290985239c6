import { useState, useEffect } from "react"
import {
  Button,
  Input,
  Card,
  CardBody,
  CardHeader,
  Checkbox,
  CheckboxGroup,
  Chip,
  Avatar,
  Radio,
  RadioGroup,
  Tabs,
  Tab
} from "@heroui/react"
import { Icon } from "@iconify/react"
import "../style.css"
import { sendToBackground } from "@plasmohq/messaging"
import { Providers } from "~lib/prividers"
import { userExportFlowers, useSearchUser } from "~hooks/useTwitter"
import FlowRadio from "~components/flow-radio"

function ExportContent() {
  const [searchUserName, setSearchUserName] = useState('')
  const [screenName, setScreenName] = useState('')
  const [activeTab, setActiveTab] = useState('search')

  const [selectedExportType, setSelectedExportType] = useState<string>('')
  // 导出状态管理
  type ExportState = 'idle' | 'exporting' | 'paused'
  const [exportState, setExportState] = useState<ExportState>('idle')

  const { data: userData, isLoading: userLoading } = useSearchUser(screenName)

  const [cursor, setCursor] = useState<string | undefined>(undefined)

  const { data, isLoading, refetch } = userExportFlowers(
    userData?.restId,
    selectedExportType,
    cursor,
    exportState === 'exporting'
  )

  console.log('%c [ Query Data ]-36', 'font-size:13px; background:pink; color:#bf2c9f;', {
    data,
    isLoading,
    exportState,
    selectedExportType,
    cursor
  })

  // 获取导出类型的显示名称
  const getExportTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      followers: '关注者',
      verifiedFollowers: '已验证的关注者',
      following: '正在关注',
      tweets: '推文',
      tweetsAndReplies: '推文和回复',
      media: '媒体推文',
      likes: '点赞的推文'
    }
    return labels[type] || type
  }

  // 从 URL 参数中获取初始数据
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const username = urlParams.get('username')

    if (username) {
      setSearchUserName(username)
      setScreenName(username)
    }
  }, [])

  // 处理数据获取完成后的游标更新和定时请求
  useEffect(() => {
    if (data?.cursor?.bottom && exportState === 'exporting') {
      // 获取下一页的游标
      const nextCursor = data.cursor.bottom.value
      if (nextCursor !== cursor) {
        // 设置定时器，2秒后更新 cursor 并触发下一次请求
        const timer = setTimeout(() => {
          setCursor(nextCursor)
          // 手动触发 refetch 以确保使用新的 cursor
          refetch()
        }, 2000) // 2秒间隔，与 hook 中的配置保持一致

        return () => clearTimeout(timer)
      }
    }
  }, [data, cursor, exportState, refetch])
  


  // 搜索用户的函数
  const handleSearch = async () => {
    setScreenName(searchUserName.trim())
  }

  // 导出数据的函数
  const handleExport = async () => {
    if (!userData || !selectedExportType) return

    if (exportState === 'idle' || exportState === 'paused') {
      // 开始或继续导出
      if (exportState === 'idle') {
        // 重置游标并开始导出
        setCursor(undefined)
      }
      setExportState('exporting')
      refetch()
    } else if (exportState === 'exporting') {
      // 暂停导出
      setExportState('paused')
    }
    // try {
    //   console.log('开始导出:', {
    //     user: userData.legacy.screenName,
    //     type: selectedExportType,
    //     typeLabel: getExportTypeLabel(selectedExportType)
    //   })

    //   // 根据选择的类型执行不同的导出逻辑
    //   switch (selectedExportType) {
    //     case 'followers':
    //       console.log('导出关注者数据...')
    //       // 这里可以使用 infinite query 的数据
    //       if (data?.pages) {
    //         // 根据实际的数据结构访问用户数据
    //         const allFollowers = data.pages.flatMap((page: any) => {
    //           // 尝试不同的数据路径
    //           return page?.data?.data || page?.data || []
    //         })
    //         console.log(`获取到 ${allFollowers.length} 个关注者`)
    //       }
    //       break
    //     case 'verifiedFollowers':
    //       console.log('导出已验证关注者数据...')
    //       break
    //     case 'following':
    //       console.log('导出关注列表数据...')
    //       break
    //     case 'tweets':
    //       console.log('导出推文数据...')
    //       break
    //     default:
    //       console.log('导出其他类型数据...')
    //   }

    //   // 模拟导出过程
    //   await new Promise(resolve => setTimeout(resolve, 2000))

    // } catch (error) {
    //   console.error('导出失败:', error)
    //   alert('导出失败，请重试')
    // } finally {
    //   setIsExporting(false)
    // }
  }

  // 获取按钮文本和状态
  const getButtonProps = () => {
    switch (exportState) {
      case 'idle':
        return {
          text: selectedExportType ? `导出${getExportTypeLabel(selectedExportType)}` : '请选择导出类型',
          loading: false,
          disabled: !selectedExportType
        }
      case 'exporting':
        return {
          text: '暂停导出',
          loading: isLoading,
          disabled: false
        }
      case 'paused':
        return {
          text: '继续导出',
          loading: false,
          disabled: false
        }
    }
  }

  const buttonProps = getButtonProps()

  return (
    <div className="h-full p-6">
      <div className="max-w-5xl mx-auto my-10">
        {/* 标题部分 */}
        <div className="text-center my-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Icon icon="logos:twitter" className="w-8 h-8" />
            <h1 className="text-3xl font-bold text-gray-800">Twitter 用户数据导出</h1>
          </div>
          <p className="text-gray-600">搜索用户并导出其关注者、正在关注和推文数据</p>
        </div>

        {/* 搜索框 */}
        <Card className="mb-6">
          <CardBody>
            <div className="flex gap-3">
              <Input
                value={searchUserName}
                onChange={(e) => setSearchUserName(e.target.value)}
                placeholder="UserName..."
                className="flex-1"
                size="lg"
                startContent={<Icon icon="gravity-ui:at" className="w-5 h-5 text-gray-400" />}
                onKeyDown={(e: React.KeyboardEvent) => e.key === 'Enter' && handleSearch()}
              />

              <Button
                color="primary"
                size="lg"
                onPress={handleSearch}
                isLoading={userLoading}
                isDisabled={!searchUserName.trim()}
              >
                搜索
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* 用户信息卡片 */}
        {userData && (
          <Card className="mb-6">
            {/* <CardHeader>
              <div className="flex items-center gap-2">
                <Icon icon="heroicons:user" className="w-5 h-5 text-gray-500" />
                <span className="font-semibold text-gray-700">用户资料</span>
              </div>
            </CardHeader> */}
            <CardBody>
              <div className="flex items-start gap-2">
                <Avatar
                  src={userData.legacy.profileImageUrlHttps}
                  name={userData.legacy.name}
                  size="lg"
                  className="flex-shrink-0"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-xl font-bold">{userData.legacy.name}</h3>
                    {userData.isBlueVerified && (
                      <Chip size="sm" color="primary" variant="flat">
                        蓝色验证
                      </Chip>
                    )}
                  </div>
                  <p className="text-gray-600 mb-2">@{userData.legacy.screenName}</p>
                  <p className="text-gray-700 mb-2">{userData.legacy.description}</p>

                  <div className="flex flex-col items-start gap-2 text-sm text-gray-500 mb-2">
                    {userData.legacy?.location && (
                      <div className="flex items-center gap-1">
                        <Icon icon="heroicons:map-pin" className="w-4 h-4" />
                        <span>{userData.legacy.location}</span>
                      </div>
                    )}
                    {userData.legacy.createdAt && (
                      <div className="flex items-center gap-1">
                        <Icon icon="heroicons:calendar" className="w-4 h-4" />
                        <span>加入于 {userData.legacy.createdAt}</span>
                      </div>
                    )}
                  </div>

                  {/* 统计数据 */}
                  <div className="flex gap-4">
                    <div className="flex items-center gap-1">
                      <div className="text-xl font-bold">
                        {userData.legacy.friendsCount.toLocaleString()}
                      </div>
                      <div className="text-sm">正在关注</div>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="text-xl font-bold">
                        {userData.legacy.followersCount.toLocaleString()}
                      </div>
                      <div className="text-sm">关注者</div>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="text-xl font-bold">
                        {userData.legacy.statusesCount.toLocaleString()}
                      </div>
                      <div className="text-sm">推文</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {/* 导出选项 */}
        {userData && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">选择要导出的数据：</h3>
            </CardHeader>
            <CardBody className="flex flex-col gap-3">
              <RadioGroup
                value={selectedExportType}
                onValueChange={setSelectedExportType}
                classNames={{ wrapper: "grid grid-cols-4" }}
              >
                <FlowRadio
                  label={userData.legacy.followersCount.toLocaleString()}
                  value="verifiedFlowers"
                  description='已验证的关注者'
                  disabled={userData.legacy.followersCount === 0}
                />
                <FlowRadio
                  label={userData.legacy.followersCount.toLocaleString()}
                  value="flowers"
                  description='关注者'
                  disabled={userData.legacy.followersCount === 0}
                />
                <FlowRadio
                  label={userData.legacy.friendsCount.toLocaleString()}
                  value="flowing"
                  description='正在关注'
                  disabled={userData.legacy.friendsCount === 0}
                />
                <FlowRadio
                  label={userData.legacy.statusesCount.toLocaleString()}
                  value="tweets"
                  description='推文'
                  disabled={userData.legacy.statusesCount === 0}
                />
              </RadioGroup>

              <Button
                color="success"
                size="lg"
                className="w-full"
                onPress={handleExport}
                // isLoading={buttonProps.loading}
                isDisabled={buttonProps.disabled}
              >
                {buttonProps.text}
              </Button>
            </CardBody>
          </Card>
        )}
      </div>
    </div>
  )
}

function ExportPage() {
  return (
    <Providers>
      <ExportContent />
    </Providers>
  )
}

export default ExportPage
