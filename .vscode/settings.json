{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.preferences.importModuleSpecifier": "shortest", "typescript.preferences.importModuleSpecifierEnding": "minimal", "typescript.suggest.includeAutomaticOptionalChainCompletions": true, "typescript.suggest.completeFunctionCalls": true, "typescript.suggest.enabled": true, "typescript.suggest.paths": true, "typescript.workspaceSymbols.scope": "currentProject", "typescript.preferences.useLabelDetailsInCompletionEntries": true, "typescript.suggest.includeCompletionsForModuleExports": true, "typescript.suggest.includeCompletionsWithSnippetText": true, "typescript.suggest.objectLiteralMethodSnippets.enabled": false}