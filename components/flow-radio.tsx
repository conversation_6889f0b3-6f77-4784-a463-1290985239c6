import React, { useState, useEffect, useRef } from "react";
import { Chip, Radio, type RadioProps } from "@heroUI/react";
import { Icon } from "@iconify/react";
import { cn } from "~lib/utils";

export type FlowRadioProps = RadioProps & {
  icon?: React.ReactNode;
  label?: string;
  isExpired?: boolean;
  isRecommended?: boolean;
};

const FlowRadio = React.forwardRef<HTMLInputElement, FlowRadioProps>(
  (
    {
      label,
      children,
      description,
      icon,
      isExpired,
      isRecommended,
      classNames = {},
      className,
      ...props
    },
    ref,
  ) => {
    const [isSelected, setIsSelected] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);

    // 监听选中状态变化
    useEffect(() => {
      const container = containerRef.current;
      if (!container) return;

      const checkSelection = () => {
        // 查找 input 元素
        const input = container.querySelector('input[type="radio"]') as HTMLInputElement;
        if (input) {
          const newSelected = input.checked;
          console.log(`Radio ${props.value}: checked = ${newSelected}`);
          setIsSelected(newSelected);
        }
      };

      // 初始检查
      setTimeout(checkSelection, 100);

      // 监听点击事件
      const handleClick = (e: Event) => {
        // 如果是禁用状态，阻止点击
        if (props.isDisabled) {
          e.preventDefault();
          e.stopPropagation();
          return;
        }
        setTimeout(checkSelection, 50);
      };

      container.addEventListener('click', handleClick);

      // 监听全局的 radio 变化（因为同一组的其他 radio 被选中时，当前的会被取消选中）
      const handleGlobalChange = (e: Event) => {
        const target = e.target as HTMLInputElement;
        if (target.type === 'radio' && target.name === (container.querySelector('input')?.name)) {
          setTimeout(checkSelection, 50);
        }
      };

      document.addEventListener('change', handleGlobalChange);

      return () => {
        container.removeEventListener('click', handleClick);
        document.removeEventListener('change', handleGlobalChange);
      };
    }, [props.value]);

    return (
      <div
        ref={containerRef}
        className={cn(
          "relative",
          props.isDisabled && "group"
        )}
      >
        <Radio
          ref={ref}
          {...props}
          classNames={{
            ...classNames,
            base: cn(
              "inline-flex m-0 px-3 py-4 max-w-[100%] items-center justify-between",
              "flex-row-reverse w-full rounded-lg border-2 transition-all duration-200",
              // 根据状态控制样式
              props.isDisabled
                ? "cursor-not-allowed border-default-100 bg-gradient-to-br from-default-50 to-default-100 opacity-60 shadow-none"
                : "cursor-pointer shadow-sm hover:shadow-md",
              // 选中状态样式（只在非禁用时生效）
              !props.isDisabled && isSelected
                ? "border-primary bg-primary/5 shadow-sm"
                : !props.isDisabled
                ? "border-default-200 hover:border-default-300"
                : "border-default-100",
              classNames?.base,
              className,
            ),
            labelWrapper: cn("ml-0", classNames?.labelWrapper),
          }}
          color="primary"
        >
          <div className="flex w-full items-center gap-3">
            <div className={cn(
              "item-center flex rounded-small p-2 relative",
              props.isDisabled && "opacity-50"
            )}>
              {icon}
              {props.isDisabled && (
                <div className="absolute -top-1 -right-1 bg-default-100 rounded-full p-1">
                  <Icon
                    icon="heroicons:no-symbol"
                    className="w-3 h-3 text-default-400"
                  />
                </div>
              )}
            </div>
            <div className="flex w-full flex-col gap-1">
              <div className="flex items-center gap-3">
                <p className={cn(
                  "text-small",
                  props.isDisabled ? "text-default-300" : "text-foreground"
                )}>
                  {label}
                </p>
                {isExpired && !props.isDisabled && (
                  <Chip className="h-6 p-0 text-tiny" color="danger">
                    Expired
                  </Chip>
                )}
                {isRecommended && !props.isDisabled && (
                  <Chip className="h-6 p-0 text-tiny" color="success" variant="flat">
                    Recommended
                  </Chip>
                )}
              </div>
              <p className={cn(
                "text-tiny",
                props.isDisabled ? "text-default-200" : "text-default-400"
              )}>
                {description || children}
              </p>
            </div>
          </div>
        </Radio>
      </div>
    );
  }
);

FlowRadio.displayName = "PaymentMethodRadio";

export default FlowRadio;
