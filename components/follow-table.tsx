import React, { useState, useEffect, useRef } from "react";
import { Chip, Radio, Table, TableBody, TableCell, TableColumn, TableHeader, TableRow, User, type RadioProps } from "@heroUI/react";
import { Icon } from "@iconify/react";
import { cn } from "~lib/utils";

export type FlowRadioProps = RadioProps & {
  icon?: React.ReactNode;
  label?: string;
  isExpired?: boolean;
  isRecommended?: boolean;
};

const FollowTable = React.forwardRef<HTMLInputElement, FlowRadioProps>(
  (
    {
      label,
      children,
      description,
      icon,
      isExpired,
      isRecommended,
      classNames = {},
      className,
      ...props
    },
    ref,
  ) => {

    return (
      <Table removeWrapper aria-label="Followers table">
        <TableHeader>
          <TableColumn><Checkbox /></TableColumn>
          <TableColumn>User</TableColumn>
          <TableColumn><Icon icon="lucide:message-square" /></TableColumn>
          <TableColumn><Icon icon="lucide:users" /></TableColumn>
          <TableColumn>Following</TableColumn>
          <TableColumn><Icon icon="lucide:heart" /></TableColumn>
          <TableColumn><Icon icon="lucide:image" /></TableColumn>
          <TableColumn>Location</TableColumn>
          <TableColumn>Status</TableColumn>
          <TableColumn><Icon icon="lucide:calendar" /></TableColumn>
        </TableHeader>
        <TableBody>
          {followers.map((follower) => (
            <TableRow key={follower.id}>
              <TableCell><Checkbox /></TableCell>
              <TableCell>
                <User
                  name={follower.name}
                  description={follower.username}
                  avatarProps={{
                    src: `https://img.heroui.chat/image/avatar?w=40&h=40&u=${follower.username}`
                  }}
                />
              </TableCell>
              <TableCell>{follower.tweets}</TableCell>
              <TableCell>{follower.following}</TableCell>
              <TableCell>{follower.followers}</TableCell>
              <TableCell>{follower.likes}</TableCell>
              <TableCell>{follower.media}</TableCell>
              <TableCell>{follower.location}</TableCell>
              <TableCell>
                {follower.location && (
                  <Button size="sm" variant="bordered" className="text-blue-500">
                    <Icon icon="lucide:external-link" className="mr-1" />
                    View
                  </Button>
                )}
              </TableCell>
              <TableCell>{follower.joinDate}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  }
);

FollowTable.displayName = "FollowTable";

export default FollowTable;
